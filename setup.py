#!/usr/bin/env python
"""
Setup script for the LIPS Web Application.
This script automates the setup process for the application.
"""

import os
import sys
import subprocess
import platform
import random
import string
import time
import getpass
from pathlib import Path

# Colors for terminal output
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

def print_header(message):
    """Print a formatted header message."""
    print(f"\n{Colors.HEADER}{Colors.BOLD}=== {message} ==={Colors.ENDC}")

def print_step(message):
    """Print a step message."""
    print(f"{Colors.BLUE}→ {message}{Colors.ENDC}")

def print_success(message):
    """Print a success message."""
    print(f"{Colors.GREEN}✓ {message}{Colors.ENDC}")

def print_warning(message):
    """Print a warning message."""
    print(f"{Colors.WARNING}⚠ {message}{Colors.ENDC}")

def print_error(message):
    """Print an error message."""
    print(f"{Colors.FAIL}✗ {message}{Colors.ENDC}")

def run_command(command, shell=False, check=True, capture_output=False):
    """Run a command and return the result."""
    try:
        if capture_output:
            result = subprocess.run(command, shell=shell, check=check, text=True, capture_output=True)
            return result
        else:
            subprocess.run(command, shell=shell, check=check)
            return None
    except subprocess.CalledProcessError as e:
        print_error(f"Command failed: {e}")
        return None

def check_python_version():
    """Check if Python version is 3.8 or higher."""
    print_step("Checking Python version...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print_error(f"Python 3.8+ is required. You have Python {version.major}.{version.minor}.{version.micro}")
        return False
    print_success(f"Python {version.major}.{version.minor}.{version.micro} detected")
    return True

def check_pip():
    """Check if pip is installed."""
    print_step("Checking pip installation...")
    try:
        run_command([sys.executable, "-m", "pip", "--version"], capture_output=True)
        print_success("pip is installed")
        return True
    except Exception:
        print_error("pip is not installed")
        return False

def setup_virtual_env():
    """Set up a virtual environment."""
    print_step("Checking virtual environment...")
    venv_dir = "venv"

    # Check if venv already exists
    if os.path.exists(venv_dir):
        print_success("Virtual environment exists")
        return True
    else:
        print_warning("Virtual environment not found. It should have been created by the run script.")
        print_step("Attempting to create virtual environment...")
        try:
            run_command([sys.executable, "-m", "venv", venv_dir])
            print_success("Virtual environment created")
            return True
        except Exception as e:
            print_error(f"Failed to create virtual environment: {e}")
            return False

def activate_virtual_env():
    """Get the python and pip paths from the virtual environment."""
    venv_dir = "venv"

    if platform.system() == "Windows":
        python_path = os.path.join(venv_dir, "Scripts", "python.exe")
        pip_path = os.path.join(venv_dir, "Scripts", "pip.exe")
    else:
        python_path = os.path.join(venv_dir, "bin", "python")
        pip_path = os.path.join(venv_dir, "bin", "pip")

    if not os.path.exists(python_path):
        print_error(f"Virtual environment Python not found at {python_path}")
        print_warning("The virtual environment may not be set up correctly.")
        print_step("Attempting to use system Python instead...")

        # Fall back to system Python
        if platform.system() == "Windows":
            return "python", "pip"
        else:
            return "python3", "pip3"

    print_success("Using virtual environment Python")
    return python_path, pip_path

def install_dependencies(pip_path):
    """Install required dependencies."""
    print_step("Installing dependencies...")
    try:
        # Install from requirements.txt
        run_command([pip_path, "install", "-r", "requirements.txt"])

        # Explicitly install PyMySQL to ensure it's available
        print_step("Ensuring PyMySQL is installed...")
        run_command([pip_path, "install", "PyMySQL"])

        print_success("Dependencies installed")
        return True
    except Exception as e:
        print_error(f"Failed to install dependencies: {e}")
        return False

def check_mysql():
    """Check if MySQL is installed and running."""
    print_step("Checking MySQL installation...")

    if platform.system() == "Windows":
        # Check for MySQL on Windows
        try:
            result = run_command(["sc", "query", "mysql"], capture_output=True, check=False)
            if result and "RUNNING" in result.stdout:
                print_success("MySQL is installed and running")
                return True
        except:
            pass

        # Try another method
        try:
            result = run_command(["mysql", "--version"], shell=True, capture_output=True, check=False)
            if result and result.returncode == 0:
                print_success("MySQL is installed")
                return True
        except:
            pass
    else:
        # Check for MySQL on Unix-like systems
        try:
            result = run_command(["systemctl", "status", "mysql"], capture_output=True, check=False)
            if result and "active (running)" in result.stdout:
                print_success("MySQL is installed and running")
                return True
        except:
            pass

        # Try another method
        try:
            result = run_command(["mysql", "--version"], shell=True, capture_output=True, check=False)
            if result and result.returncode == 0:
                print_success("MySQL is installed")
                return True
        except:
            pass

    print_warning("MySQL might not be installed or running")
    print_warning("You'll need to install MySQL and create a database named 'lipsweb'")

    # Ask user if they want to continue
    response = input("Do you want to continue anyway? (y/n): ").lower()
    return response == 'y'

def create_env_file():
    """Create .env file with necessary configuration."""
    print_step("Creating .env file...")

    # Check if .env already exists
    if os.path.exists(".env"):
        print_warning(".env file already exists")
        response = input("Do you want to overwrite it? (y/n): ").lower()
        if response != 'y':
            print_step("Using existing .env file")
            return True

    # Generate a random secret key
    secret_key = ''.join(random.choice(string.ascii_letters + string.digits + string.punctuation) for _ in range(50))

    # Get database configuration from user
    print_step("Please provide your MySQL database configuration:")
    db_name = input("Database name (default: lipsweb): ") or "lipsweb"
    db_user = input("Database user (default: root): ") or "root"
    db_password = getpass.getpass("Database password (default: empty): ") or ""
    db_host = input("Database host (default: localhost): ") or "localhost"
    db_port = input("Database port (default: 3306): ") or "3306"

    # Create .env file
    env_content = f"""DJANGO_SECRET_KEY={secret_key}
DEBUG=True
DB_NAME={db_name}
DB_USER={db_user}
DB_PASSWORD={db_password}
DB_HOST={db_host}
DB_PORT={db_port}
"""

    try:
        with open(".env", "w") as f:
            f.write(env_content)
        print_success(".env file created")
        return True
    except Exception as e:
        print_error(f"Failed to create .env file: {e}")
        return False

def run_migrations(python_path):
    """Run database migrations."""
    print_step("Running database migrations...")
    try:
        result = run_command([python_path, "manage.py", "migrate"], capture_output=True)
        if result and result.returncode != 0:
            print_error(f"Migration failed: {result.stderr}")
            return False
        print_success("Migrations completed")
        return True
    except Exception as e:
        print_error(f"Failed to run migrations: {e}")
        return False

def create_roles(python_path):
    """Create initial roles."""
    print_step("Creating initial roles...")

    # Create a temporary script to create roles
    script_content = """
from django.core.management.base import BaseCommand
from usermanagement.models import Role

class Command(BaseCommand):
    help = 'Create initial roles'

    def handle(self, *args, **kwargs):
        roles = ['admin', 'manager', 'user']
        for i, role_name in enumerate(roles, 1):
            role, created = Role.objects.get_or_create(id=i, defaults={'name': role_name})
            if created:
                self.stdout.write(self.style.SUCCESS(f'Created role: {role_name}'))
            else:
                self.stdout.write(f'Role already exists: {role_name}')
"""

    try:
        # Create the directory structure
        os.makedirs("usermanagement/management/commands", exist_ok=True)

        # Write the command file
        with open("usermanagement/management/commands/create_roles.py", "w") as f:
            f.write(script_content)

        # Run the command and capture output
        result = run_command([python_path, "manage.py", "create_roles"], capture_output=True)
        if result and result.returncode != 0:
            print_error(f"Failed to create roles: {result.stderr}")

            # Alternative approach: create roles directly using Django shell
            print_step("Trying alternative approach to create roles...")
            shell_command = f"""
from usermanagement.models import Role
Role.objects.get_or_create(id=1, defaults={{'name': 'admin'}})
Role.objects.get_or_create(id=2, defaults={{'name': 'manager'}})
Role.objects.get_or_create(id=3, defaults={{'name': 'user'}})
print('Roles created successfully')
"""
            shell_result = run_command([python_path, "manage.py", "shell", "-c", shell_command], capture_output=True)
            if shell_result and shell_result.returncode != 0:
                print_error(f"Alternative approach failed: {shell_result.stderr}")
                return False
            else:
                print_success("Initial roles created using alternative approach")
                return True

        print_success("Initial roles created")
        return True
    except Exception as e:
        print_error(f"Failed to create roles: {e}")
        return False

def create_superuser(python_path):
    """Create a superuser if one doesn't exist."""
    print_step("Checking for existing superuser...")

    # Create a temporary script to check and create superuser
    script_content = """
from django.core.management.base import BaseCommand
from usermanagement.models import User, Role
from django.contrib.auth.hashers import make_password

class Command(BaseCommand):
    help = 'Create a superuser if one does not exist'

    def add_arguments(self, parser):
        parser.add_argument('--username', type=str, help='Superuser username')
        parser.add_argument('--password', type=str, help='Superuser password')
        parser.add_argument('--email', type=str, help='Superuser email')

    def handle(self, *args, **kwargs):
        if User.objects.filter(role__name='admin').exists():
            self.stdout.write('Admin user already exists')
            return

        username = kwargs['username']
        password = kwargs['password']
        email = kwargs['email']

        admin_role, _ = Role.objects.get_or_create(name='admin')

        User.objects.create(
            username=username,
            password=make_password(password),
            email=email,
            role=admin_role,
            full_name='Admin User',
            status=True
        )

        self.stdout.write(self.style.SUCCESS(f'Superuser {username} created successfully'))
"""

    try:
        # Create the directory structure if it doesn't exist
        os.makedirs("usermanagement/management/commands", exist_ok=True)

        # Write the command file
        with open("usermanagement/management/commands/create_superuser_custom.py", "w") as f:
            f.write(script_content)

        # Check if superuser exists
        check_result = run_command([python_path, "manage.py", "create_superuser_custom"], capture_output=True, check=False)
        if check_result and check_result.returncode == 0 and "Admin user already exists" in check_result.stdout:
            print_success("Admin user already exists")
            return True

        # If the command failed but not because the user doesn't exist
        if check_result and check_result.returncode != 0:
            print_error(f"Error checking for existing superuser: {check_result.stderr}")

            # Try alternative approach using Django shell
            print_step("Trying alternative approach to create admin user...")

            # Create superuser if it doesn't exist
            username = input("Admin username (default: admin): ") or "admin"
            password = getpass.getpass("Admin password (default: admin): ") or "admin"
            email = input("Admin email (default: <EMAIL>): ") or "<EMAIL>"

            shell_command = f"""
from usermanagement.models import User, Role
from django.contrib.auth.hashers import make_password

# Check if admin user exists
if not User.objects.filter(role__name='admin').exists():
    # Create admin user
    admin_role, _ = Role.objects.get_or_create(name='admin')
    User.objects.create(
        username='{username}',
        password=make_password('{password}'),
        email='{email}',
        role=admin_role,
        full_name='Admin User',
        status=True
    )
    print(f'Superuser {username} created successfully')
else:
    print('Admin user already exists')
"""
            shell_result = run_command([python_path, "manage.py", "shell", "-c", shell_command], capture_output=True)
            if shell_result and shell_result.returncode != 0:
                print_error(f"Alternative approach failed: {shell_result.stderr}")
                return False
            elif "created successfully" in shell_result.stdout:
                print_success(f"Admin user '{username}' created successfully")
                return True
            else:
                print_success("Admin user already exists")
                return True

        # Create superuser if it doesn't exist
        print_step("Creating a new admin user...")
        username = input("Admin username (default: admin): ") or "admin"
        password = getpass.getpass("Admin password (default: admin): ") or "admin"
        email = input("Admin email (default: <EMAIL>): ") or "<EMAIL>"

        result = run_command([
            python_path, "manage.py", "create_superuser_custom",
            "--username", username,
            "--password", password,
            "--email", email
        ], capture_output=True)

        if result and result.returncode != 0:
            print_error(f"Failed to create superuser: {result.stderr}")
            return False

        print_success(f"Admin user '{username}' created successfully")
        return True
    except Exception as e:
        print_error(f"Failed to create superuser: {e}")
        return False

def generate_token(python_path):
    """Generate an access token for the admin user."""
    print_step("Generating access token...")

    try:
        result = run_command([python_path, "generate_token.py"], capture_output=True)
        if result and result.returncode != 0:
            print_error(f"Failed to generate token: {result.stderr}")
            print_warning("Continuing without token generation")
            return True  # Continue anyway as this is not critical

        if result and result.stdout:
            print_success("Access token generated successfully")
            print(result.stdout)
        return True
    except Exception as e:
        print_error(f"Failed to generate token: {e}")
        print_warning("Continuing without token generation")
        return True  # Continue anyway as this is not critical

def start_server(python_path, port=None):
    """Start the Django development server."""
    print_header("Starting Django Server")
    print_success("Setup completed successfully!")
    print_step("Starting the Django development server...")
    print_warning("Press Ctrl+C to stop the server")

    try:
        if port:
            run_command([python_path, "manage.py", "runserver", f"0.0.0.0:{port}"])
        else:
            run_command([python_path, "manage.py", "runserver"])
        return True
    except KeyboardInterrupt:
        print_step("\nServer stopped")
        return True
    except Exception as e:
        print_error(f"Failed to start server: {e}")
        return False

def main():
    """Main function to run the setup process."""
    print_header("LIPS Web Application Setup")

    # Check for custom port
    port = None
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
            print_step(f"Using custom port: {port}")
        except ValueError:
            print_warning(f"Invalid port number: {sys.argv[1]}. Using default port 8000.")

    # Check Python version
    if not check_python_version():
        sys.exit(1)

    # Check pip
    if not check_pip():
        sys.exit(1)

    # Setup virtual environment
    if not setup_virtual_env():
        sys.exit(1)

    # Activate virtual environment
    python_path, pip_path = activate_virtual_env()
    if not python_path or not pip_path:
        sys.exit(1)

    # Install dependencies
    if not install_dependencies(pip_path):
        sys.exit(1)

    # Check MySQL
    if not check_mysql():
        sys.exit(1)

    # Create .env file
    if not create_env_file():
        sys.exit(1)

    # Run migrations
    if not run_migrations(python_path):
        sys.exit(1)

    # Create roles
    if not create_roles(python_path):
        sys.exit(1)

    # Create superuser
    if not create_superuser(python_path):
        sys.exit(1)

    # Generate token
    generate_token(python_path)

    # Start server
    start_server(python_path, port)

if __name__ == "__main__":
    main()
