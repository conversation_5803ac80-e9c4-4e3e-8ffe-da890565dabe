#!/usr/bin/env python3
"""
Test script for all newly created APIs based on Postman collection
"""

import requests
import json
import sys
import time

# Base URL for the API
BASE_URL = "http://localhost:8001"

class APITester:
    def __init__(self):
        self.session = requests.Session()
        self.token = None
        self.device_id = None
        self.work_id = None
        
    def authenticate(self):
        """Authenticate and get access token"""
        print("🔐 Testing Authentication...")
        
        # Try to login with default credentials
        login_data = {
            "email": "<EMAIL>",
            "password": "admin123"
        }
        
        response = self.session.post(f"{BASE_URL}/api/v1/auth/login/", json=login_data)
        
        if response.status_code == 200:
            data = response.json()
            if 'access' in data:
                self.token = data['access']
                self.session.headers.update({'Authorization': f'Bearer {self.token}'})
                print("✅ Authentication successful")
                return True
            else:
                print("❌ Authentication failed: No access token in response")
                return False
        else:
            print(f"❌ Authentication failed: {response.status_code} - {response.text}")
            return False

    def test_device_crud_apis(self):
        """Test Device CRUD APIs"""
        print("\n📱 Testing Device Management APIs...")
        
        # 1. Test GET all devices
        print("\n1️⃣ Testing GET /api/v1/lipsapi/device/ (List all devices)")
        response = self.session.get(f"{BASE_URL}/api/v1/lipsapi/device/")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success: Found {len(data.get('data', {}).get('devices', []))} devices")
        else:
            print(f"❌ Failed: {response.text}")

        # 2. Test POST create device
        print("\n2️⃣ Testing POST /api/v1/lipsapi/device/ (Create device)")
        device_data = {
            "name": "Test Device API",
            "deviceId": f"TEST_DEVICE_{int(time.time())}",
            "charge": 85,
            "status": "active"
        }
        
        response = self.session.post(f"{BASE_URL}/api/v1/lipsapi/device/", json=device_data)
        print(f"Status: {response.status_code}")
        if response.status_code == 201:
            data = response.json()
            self.device_id = data.get('data', {}).get('id')
            print(f"✅ Success: Created device with ID {self.device_id}")
            print(f"Device details: {json.dumps(data.get('data'), indent=2)}")
        else:
            print(f"❌ Failed: {response.text}")

        # 3. Test GET specific device
        if self.device_id:
            print(f"\n3️⃣ Testing GET /api/v1/lipsapi/device/{self.device_id}/ (Get device by ID)")
            response = self.session.get(f"{BASE_URL}/api/v1/lipsapi/device/{self.device_id}/")
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Success: Retrieved device {self.device_id}")
                print(f"Device details: {json.dumps(data.get('data'), indent=2)}")
            else:
                print(f"❌ Failed: {response.text}")

        # 4. Test PUT update device
        if self.device_id:
            print(f"\n4️⃣ Testing PUT /api/v1/lipsapi/device/{self.device_id}/ (Update device)")
            update_data = {
                "name": "Updated Test Device",
                "charge": 90,
                "status": "active"
            }
            
            response = self.session.put(f"{BASE_URL}/api/v1/lipsapi/device/{self.device_id}/", json=update_data)
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Success: Updated device {self.device_id}")
                print(f"Updated device: {json.dumps(data.get('data'), indent=2)}")
            else:
                print(f"❌ Failed: {response.text}")

    def test_device_timer_apis(self):
        """Test Device Timer Management APIs"""
        print("\n⏱️ Testing Device Timer Management APIs...")
        
        if not self.device_id:
            print("❌ Skipping timer tests - no device ID available")
            return

        # Create a work first
        work_data = {
            "work_name": f"Test Work {int(time.time())}",
            "group_num": "TEST_GROUP"
        }
        
        response = self.session.post(f"{BASE_URL}/api/v1/lipsapi/works/", json=work_data)
        if response.status_code == 201:
            work_response = response.json()
            self.work_id = work_response.get('data', {}).get('id')
            print(f"✅ Created test work with ID {self.work_id}")
        
        if self.work_id:
            # Test timer reset
            print(f"\n1️⃣ Testing POST /api/v1/lipsapi/device/{self.device_id}/timer/reset/ (Reset device timer)")
            timer_data = {
                "work_id": self.work_id
            }
            
            response = self.session.post(f"{BASE_URL}/api/v1/lipsapi/device/{self.device_id}/timer/reset/", json=timer_data)
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Success: Timer reset for device {self.device_id}")
                print(f"Timer data: {json.dumps(data.get('data'), indent=2)}")
            else:
                print(f"❌ Failed: {response.text}")

            # Test get work time
            print(f"\n2️⃣ Testing GET /api/v1/lipsapi/device/{self.device_id}/work-time/ (Get work time)")
            response = self.session.get(f"{BASE_URL}/api/v1/lipsapi/device/{self.device_id}/work-time/")
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Success: Retrieved work time for device {self.device_id}")
                print(f"Work time data: {json.dumps(data.get('data'), indent=2)}")
            else:
                print(f"❌ Failed: {response.text}")

    def test_work_time_calculation_apis(self):
        """Test Work Time Calculation APIs"""
        print("\n🕐 Testing Work Time Calculation APIs...")
        
        if not self.work_id:
            print("❌ Skipping work time tests - no work ID available")
            return

        # Test individual work time calculation
        print(f"\n1️⃣ Testing GET /api/v1/lipsapi/works/{self.work_id}/time-calculation/ (Get work time calculation)")
        response = self.session.get(f"{BASE_URL}/api/v1/lipsapi/works/{self.work_id}/time-calculation/")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success: Retrieved time calculation for work {self.work_id}")
            print(f"Time calculation: {json.dumps(data.get('data'), indent=2)}")
        else:
            print(f"❌ Failed: {response.text}")

        # Test all active works time calculation
        print(f"\n2️⃣ Testing GET /api/v1/lipsapi/works/active-works/time-calculation/ (Get all active works time)")
        response = self.session.get(f"{BASE_URL}/api/v1/lipsapi/works/active-works/time-calculation/")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success: Retrieved time calculation for all active works")
            print(f"Active works count: {len(data.get('data', []))}")
        else:
            print(f"❌ Failed: {response.text}")

        # Test work timer update
        print(f"\n3️⃣ Testing PUT /api/v1/lipsapi/works/{self.work_id}/timer/ (Update work timer)")
        timer_update_data = {
            "action": "start",
            "timestamp": "2025-06-16T13:30:00Z"
        }
        
        response = self.session.put(f"{BASE_URL}/api/v1/lipsapi/works/{self.work_id}/timer/", json=timer_update_data)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success: Updated timer for work {self.work_id}")
            print(f"Timer update: {json.dumps(data.get('data'), indent=2)}")
        else:
            print(f"❌ Failed: {response.text}")

    def test_device_settings_apis(self):
        """Test Device Settings APIs"""
        print("\n⚙️ Testing Device Settings APIs...")
        
        # Test GET all device settings
        print("\n1️⃣ Testing GET /api/v1/lipsapi/device/settings/ (Get all device settings)")
        response = self.session.get(f"{BASE_URL}/api/v1/lipsapi/device/settings/")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success: Retrieved device settings")
            print(f"Settings count: {len(data.get('data', {}).get('deviceSettings', []))}")
        else:
            print(f"❌ Failed: {response.text}")

        if self.device_id:
            # Test GET specific device settings
            print(f"\n2️⃣ Testing GET /api/v1/lipsapi/device/settings/{self.device_id}/ (Get device settings by ID)")
            response = self.session.get(f"{BASE_URL}/api/v1/lipsapi/device/settings/{self.device_id}/")
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Success: Retrieved settings for device {self.device_id}")
                print(f"Device settings: {json.dumps(data.get('data'), indent=2)}")
            else:
                print(f"❌ Failed: {response.text}")

    def test_works_apis(self):
        """Test Works Management APIs"""
        print("\n📋 Testing Works Management APIs...")
        
        # Test GET all works
        print("\n1️⃣ Testing GET /api/v1/lipsapi/works/ (Get all works)")
        response = self.session.get(f"{BASE_URL}/api/v1/lipsapi/works/")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success: Retrieved all works")
            print(f"Works count: {len(data.get('data', {}).get('works', []))}")
        else:
            print(f"❌ Failed: {response.text}")

        if self.work_id:
            # Test GET specific work
            print(f"\n2️⃣ Testing GET /api/v1/lipsapi/works/{self.work_id}/ (Get work by ID)")
            response = self.session.get(f"{BASE_URL}/api/v1/lipsapi/works/{self.work_id}/")
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Success: Retrieved work {self.work_id}")
                print(f"Work details: {json.dumps(data.get('data'), indent=2)}")
            else:
                print(f"❌ Failed: {response.text}")

    def cleanup(self):
        """Clean up test data"""
        print("\n🧹 Cleaning up test data...")
        
        # Delete test device
        if self.device_id:
            response = self.session.delete(f"{BASE_URL}/api/v1/lipsapi/device/{self.device_id}/")
            if response.status_code == 200:
                print(f"✅ Deleted test device {self.device_id}")
            else:
                print(f"❌ Failed to delete device: {response.text}")

        # Delete test work
        if self.work_id:
            response = self.session.delete(f"{BASE_URL}/api/v1/lipsapi/works/{self.work_id}/")
            if response.status_code == 200:
                print(f"✅ Deleted test work {self.work_id}")
            else:
                print(f"❌ Failed to delete work: {response.text}")

    def run_all_tests(self):
        """Run all API tests"""
        print("🚀 Starting API Tests for New Endpoints")
        print("=" * 50)
        
        # Authenticate first
        if not self.authenticate():
            print("❌ Cannot proceed without authentication")
            return False
        
        # Run all tests
        self.test_device_crud_apis()
        self.test_device_timer_apis()
        self.test_work_time_calculation_apis() 
        self.test_device_settings_apis()
        self.test_works_apis()
        
        # Cleanup
        self.cleanup()
        
        print("\n" + "=" * 50)
        print("🎉 API Testing Complete!")
        
        return True

def main():
    """Main function"""
    tester = APITester()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ All tests completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()