import os
import sys
import django
import datetime
from datetime import timedelta

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Import models after setting up Django
from usermanagement.models import Role, User
from lips.models import (
    AlertInstructionManagementInfo, UserMaster, WorkInfo, DeviceMaster,
    DeviceReceiveData, FixInfo, HistoricalJudgeAlertInstruction,
    PermitedApproachInfo, ProhibitedApproachInfo, SettingsInfo
)
from django.utils import timezone
from django.contrib.auth.hashers import make_password

def setup_roles():
    """Create default roles"""
    print("Setting up roles...")
    
    roles = ['admin', 'manager', 'user']
    for role_name in roles:
        role, created = Role.objects.get_or_create(name=role_name)
        print(f"{'Created' if created else 'Found'} role: {role.name}")

def create_users():
    """Create demo users"""
    print("\nCreating users...")
    
    # Create admin role if it doesn't exist
    admin_role, _ = Role.objects.get_or_create(name='admin')
    manager_role, _ = Role.objects.get_or_create(name='manager')
    user_role, _ = Role.objects.get_or_create(name='user')
    
    users_data = [
        {
            'username': 'admin',
            'email': '<EMAIL>',
            'password': 'admin123',
            'full_name': 'Admin User',
            'role': admin_role,
            'company_name': 'Demo Company',
            'is_staff': True,
            'is_superuser': True
        },
        {
            'username': 'manager1',
            'email': '<EMAIL>',
            'password': 'manager123',
            'full_name': 'Manager One',
            'role': manager_role,
            'company_name': 'Demo Company'
        },
        {
            'username': 'user1',
            'email': '<EMAIL>',
            'password': 'user123',
            'full_name': 'Regular User One',
            'role': user_role,
            'company_name': 'Demo Company'
        },
        {
            'username': 'user2',
            'email': '<EMAIL>',
            'password': 'user123',
            'full_name': 'Regular User Two',
            'role': user_role,
            'company_name': 'Demo Company'
        }
    ]
    
    for data in users_data:
        user, created = User.objects.get_or_create(
            username=data['username'],
            defaults={
                'email': data['email'],
                'password': make_password(data['password']),
                'full_name': data.get('full_name'),
                'role': data.get('role'),
                'company_name': data.get('company_name', ''),
                'is_staff': data.get('is_staff', False),
                'is_superuser': data.get('is_superuser', False),
                'is_active': True,
                'status': 'active'
            }
        )
        print(f"{'Created' if created else 'Found'} user: {user.username}")

def create_lips_data():
    """Create demo data for the lips app"""
    print("\nCreating LIPS app data...")
    
    # 1. Create AlertInstructionManagementInfo objects
    print("\nCreating Alert Instruction Management Info...")
    for i in range(1, 6):
        info, created = AlertInstructionManagementInfo.objects.get_or_create(id=i)
        print(f"{'Created' if created else 'Found'} alert instruction info with ID: {info.id}")

    # 2. Create UserMaster objects
    print("\nCreating User Masters...")
    user_masters = []
    for i in range(1, 6):
        user_id = f'USER00{i}'
        user, created = UserMaster.objects.get_or_create(
            id=user_id,
            defaults={
                'created_at': '2025-05-08 10:00:00',
                'updated_at': '2025-05-08 10:00:00'
            }
        )
        user_masters.append(user)
        print(f"{'Created' if created else 'Found'} user master: {user.id}")

    # 3. Create WorkInfo objects
    print("\nCreating Work Info...")
    work_info_data = [
        {'user_id': user_masters[0], 'work_name': 'SVLR0008', 'group_num': '1111'},
        {'user_id': user_masters[1], 'work_name': 'SVLR0010', 'group_num': '1112'},
        {'user_id': user_masters[2], 'work_name': 'SVLR0012', 'group_num': '2111'},
        {'user_id': user_masters[0], 'work_name': 'SVLR0015', 'group_num': '3111'},
        {'user_id': user_masters[1], 'work_name': 'SVLR0018', 'group_num': '4111'},
    ]
    
    work_infos = []
    for i, data in enumerate(work_info_data, 1):
        work, created = WorkInfo.objects.get_or_create(
            work_name=data['work_name'],
            defaults={
                'user_id': data['user_id'],
                'group_num': data['group_num'],
                'created_at': '2025-05-09 10:00:00',
                'updated_at': '2025-05-09 10:00:00'
            }
        )
        work_infos.append(work)
        print(f"{'Created' if created else 'Found'} work info: {work.work_name}")

    # 4. Create DeviceMaster objects
    print("\nCreating Device Masters...")
    device_master_data = [
        {'user_id': user_masters[0], 'display_device_id': '0010', 'device_name': 'motomachi', 'work_id': work_infos[0], 'work_time': '02:30:00', 'battery': 85, 'previous_alert_instruction': '0', 'signal_period': 30},
        {'user_id': user_masters[1], 'display_device_id': '0020', 'device_name': 'shibuya', 'work_id': work_infos[1], 'work_time': '03:45:00', 'battery': 65, 'previous_alert_instruction': '0', 'signal_period': 45},
        {'user_id': user_masters[2], 'display_device_id': '0030', 'device_name': 'akihabara', 'work_id': work_infos[2], 'work_time': '05:20:00', 'battery': 25, 'previous_alert_instruction': '1', 'signal_period': 60},
        {'user_id': user_masters[0], 'display_device_id': '0040', 'device_name': 'shinjuku', 'work_id': work_infos[3], 'work_time': '01:10:00', 'battery': 10, 'previous_alert_instruction': '1', 'signal_period': 30},
        {'user_id': user_masters[1], 'display_device_id': '0050', 'device_name': 'tokyo', 'work_id': work_infos[4], 'work_time': '00:45:00', 'battery': 90, 'previous_alert_instruction': '0', 'signal_period': 45},
    ]
    
    device_masters = []
    for data in device_master_data:
        # Create with auto-generated ID
        device, created = DeviceMaster.objects.get_or_create(
            display_device_id=data['display_device_id'],
            defaults={
                'user_id': data['user_id'],
                'device_name': data['device_name'],
                'work_id': data['work_id'],
                'work_time': data['work_time'],
                'battery': data['battery'],
                'previous_alert_instruction': data['previous_alert_instruction'],
                'signal_period': data['signal_period'],
                'created_at': timezone.now(),
                'updated_at': '2025-05-10 15:00:00'
            }
        )
        device_masters.append(device)
        print(f"{'Created' if created else 'Found'} device master: {device.device_name} ({device.id})")

    # 5. Create DeviceReceiveData objects
    print("\nCreating Device Receive Data...")
    receive_data = [
        {'id': 'DRD001', 'device_id': device_masters[0], 'time': '2025-05-11 09:00:00', 'previous_alert_id': 'ALERT001', 'battery': 85, 'dop': 5, 'ns_latitude_identifier': 'N', 'latitude': 356812, 'ew_longitude_identifier': 'E', 'longitude': 1397671, 'x_acceleration': 100, 'y_acceleration': 200, 'z_acceleration': 300, 'alive_count': 1},
        {'id': 'DRD002', 'device_id': device_masters[1], 'time': '2025-05-11 09:15:00', 'previous_alert_id': 'ALERT002', 'battery': 65, 'dop': 7, 'ns_latitude_identifier': 'N', 'latitude': 356732, 'ew_longitude_identifier': 'E', 'longitude': 1397601, 'x_acceleration': 120, 'y_acceleration': 220, 'z_acceleration': 320, 'alive_count': 2},
        {'id': 'DRD003', 'device_id': device_masters[2], 'time': '2025-05-11 09:30:00', 'previous_alert_id': 'ALERT003', 'battery': 25, 'dop': 6, 'ns_latitude_identifier': 'N', 'latitude': 356602, 'ew_longitude_identifier': 'E', 'longitude': 1397521, 'x_acceleration': 110, 'y_acceleration': 210, 'z_acceleration': 310, 'alive_count': 3},
        {'id': 'DRD004', 'device_id': device_masters[3], 'time': '2025-05-11 09:45:00', 'previous_alert_id': 'ALERT004', 'battery': 10, 'dop': 8, 'ns_latitude_identifier': 'N', 'latitude': 356502, 'ew_longitude_identifier': 'E', 'longitude': 1397421, 'x_acceleration': 130, 'y_acceleration': 230, 'z_acceleration': 330, 'alive_count': 4},
        {'id': 'DRD005', 'device_id': device_masters[4], 'time': '2025-05-11 10:00:00', 'previous_alert_id': 'ALERT005', 'battery': 90, 'dop': 4, 'ns_latitude_identifier': 'N', 'latitude': 356402, 'ew_longitude_identifier': 'E', 'longitude': 1397321, 'x_acceleration': 90, 'y_acceleration': 190, 'z_acceleration': 290, 'alive_count': 5},
    ]
    
    for data in receive_data:
        obj, created = DeviceReceiveData.objects.get_or_create(
            id=data['id'],
            defaults={
                'device_id': data['device_id'],
                'time': data['time'],
                'previous_alert_id': data['previous_alert_id'],
                'battery': data['battery'],
                'dop': data['dop'],
                'ns_latitude_identifier': data['ns_latitude_identifier'],
                'latitude': data['latitude'],
                'ew_longitude_identifier': data['ew_longitude_identifier'],
                'longitude': data['longitude'],
                'x_acceleration': data['x_acceleration'],
                'y_acceleration': data['y_acceleration'],
                'z_acceleration': data['z_acceleration'],
                'alive_count': data['alive_count'],
                'created_at': data['time'],
                'updated_at': data['time']
            }
        )
        print(f"{'Created' if created else 'Found'} device receive data: {obj.id}")

    # 6. Create FixInfo objects
    print("\nCreating Fix Info...")
    fix_info_data = [
        {'id': 'FI001', 'key_name': 'approach_threshold', 'val': '5.0', 'summary': 'Approach distance threshold'},
        {'id': 'FI002', 'key_name': 'battery_threshold', 'val': '20', 'summary': 'Battery warning threshold'},
        {'id': 'FI003', 'key_name': 'signal_interval', 'val': '30', 'summary': 'Signal interval in seconds'},
        {'id': 'FI004', 'key_name': 'offline_timeout', 'val': '300', 'summary': 'Offline timeout in seconds'},
        {'id': 'FI005', 'key_name': 'alert_expiry', 'val': '3600', 'summary': 'Alert expiry in seconds'},
    ]
    
    for data in fix_info_data:
        obj, created = FixInfo.objects.get_or_create(
            id=data['id'],
            defaults={
                'key_name': data['key_name'],
                'val': data['val'],
                'summary': data['summary'],
                'created_at': '2025-05-01 12:00:00',
                'updated_at': '2025-05-01 12:00:00'
            }
        )
        print(f"{'Created' if created else 'Found'} fix info: {obj.key_name}")

    # 7. Create HistoricalJudgeAlertInstruction objects
    print("\nCreating Historical Judge Alert Instructions...")
    hist_data = [
        {'id': 'HJA001', 'device_id': device_masters[0], 'ns_latitude_identifier': 'N', 'latitude': 356812, 'ew_longitude_identifier': 'E', 'longitude': 1397671, 'utm_x': '539750', 'utm_y': '3938100', 'x_acceleration': 100, 'y_acceleration': 200, 'z_acceleration': 300, 'alert_instruction': 0, 'alert_id': 'ALERT001', 'previous_alert_id': 'PREV001', 'work_time': '02:30:00', 'group_num': '1111', 'alive_count': 1},
        {'id': 'HJA002', 'device_id': device_masters[1], 'ns_latitude_identifier': 'N', 'latitude': 356732, 'ew_longitude_identifier': 'E', 'longitude': 1397601, 'utm_x': '539700', 'utm_y': '3938050', 'x_acceleration': 120, 'y_acceleration': 220, 'z_acceleration': 320, 'alert_instruction': 1, 'alert_id': 'ALERT002', 'previous_alert_id': 'PREV002', 'work_time': '03:45:00', 'group_num': '1112', 'alive_count': 2},
        {'id': 'HJA003', 'device_id': device_masters[2], 'ns_latitude_identifier': 'N', 'latitude': 356602, 'ew_longitude_identifier': 'E', 'longitude': 1397521, 'utm_x': '539650', 'utm_y': '3938000', 'x_acceleration': 110, 'y_acceleration': 210, 'z_acceleration': 310, 'alert_instruction': 1, 'alert_id': 'ALERT003', 'previous_alert_id': 'PREV003', 'work_time': '05:20:00', 'group_num': '2111', 'alive_count': 3},
        {'id': 'HJA004', 'device_id': device_masters[3], 'ns_latitude_identifier': 'N', 'latitude': 356502, 'ew_longitude_identifier': 'E', 'longitude': 1397421, 'utm_x': '539600', 'utm_y': '3937950', 'x_acceleration': 130, 'y_acceleration': 230, 'z_acceleration': 330, 'alert_instruction': 0, 'alert_id': 'ALERT004', 'previous_alert_id': 'PREV004', 'work_time': '01:10:00', 'group_num': '3111', 'alive_count': 4},
        {'id': 'HJA005', 'device_id': device_masters[4], 'ns_latitude_identifier': 'N', 'latitude': 356402, 'ew_longitude_identifier': 'E', 'longitude': 1397321, 'utm_x': '539550', 'utm_y': '3937900', 'x_acceleration': 90, 'y_acceleration': 190, 'z_acceleration': 290, 'alert_instruction': 0, 'alert_id': 'ALERT005', 'previous_alert_id': 'PREV005', 'work_time': '00:45:00', 'group_num': '4111', 'alive_count': 5},
    ]
    
    for data in hist_data:
        obj, created = HistoricalJudgeAlertInstruction.objects.get_or_create(
            id=data['id'],
            defaults={
                'device_id': data['device_id'],
                'ns_latitude_identifier': data['ns_latitude_identifier'],
                'latitude': data['latitude'],
                'ew_longitude_identifier': data['ew_longitude_identifier'],
                'longitude': data['longitude'],
                'utm_x': data['utm_x'],
                'utm_y': data['utm_y'],
                'x_acceleration': data['x_acceleration'],
                'y_acceleration': data['y_acceleration'],
                'z_acceleration': data['z_acceleration'],
                'alert_instruction': data['alert_instruction'],
                'alert_id': data['alert_id'],
                'previous_alert_id': data['previous_alert_id'],
                'work_time': data['work_time'],
                'group_num': data['group_num'],
                'alive_count': data['alive_count'],
                'created_at': '2025-05-11 09:00:00',
                'updated_at': '2025-05-11 09:00:00'
            }
        )
        print(f"{'Created' if created else 'Found'} historical alert: {obj.id}")

    # 8. Create PermitedApproachInfo objects
    print("\nCreating Permitted Approach Info...")
    permit_data = [
        {'id': 'PA001', 'area_element_num': 1, 'work_id': work_infos[0], 'area_info': '{"type":"Polygon","coordinates":[[[139.7671,35.6812],[139.7701,35.6832],[139.7721,35.6802],[139.7691,35.6782],[139.7671,35.6812]]]}'},
        {'id': 'PA002', 'area_element_num': 2, 'work_id': work_infos[1], 'area_info': '{"type":"Polygon","coordinates":[[[139.7571,35.6712],[139.7601,35.6732],[139.7621,35.6702],[139.7591,35.6682],[139.7571,35.6712]]]}'},
        {'id': 'PA003', 'area_element_num': 3, 'work_id': work_infos[2], 'area_info': '{"type":"Polygon","coordinates":[[[139.7471,35.6612],[139.7501,35.6632],[139.7521,35.6602],[139.7491,35.6582],[139.7471,35.6612]]]}'},
        {'id': 'PA004', 'area_element_num': 4, 'work_id': work_infos[3], 'area_info': '{"type":"Polygon","coordinates":[[[139.7371,35.6512],[139.7401,35.6532],[139.7421,35.6502],[139.7391,35.6482],[139.7371,35.6512]]]}'},
        {'id': 'PA005', 'area_element_num': 5, 'work_id': work_infos[4], 'area_info': '{"type":"Polygon","coordinates":[[[139.7271,35.6412],[139.7301,35.6432],[139.7321,35.6402],[139.7291,35.6382],[139.7271,35.6412]]]}'},
    ]
    
    for data in permit_data:
        obj, created = PermitedApproachInfo.objects.get_or_create(
            id=data['id'],
            defaults={
                'area_element_num': data['area_element_num'],
                'work_id': data['work_id'],
                'area_info': data['area_info'],
                'created_at': '2025-05-05 10:00:00',
                'updated_at': '2025-05-05 10:00:00'
            }
        )
        print(f"{'Created' if created else 'Found'} permitted approach info: {obj.id}")

    # 9. Create ProhibitedApproachInfo objects
    print("\nCreating Prohibited Approach Info...")
    prohibit_data = [
        {'id': 'PRA001', 'latitude': 35.6812, 'longitude': 139.7671, 'x_vector': '10', 'y_vector': '20', 'base_area': 'Tokyo Area 1', 'extraction_area': 'Station', 'map_code': 'MAP001', 'prefectures': 'Tokyo', 'municipalities': 'Chiyoda'},
        {'id': 'PRA002', 'latitude': 35.6712, 'longitude': 139.7571, 'x_vector': '15', 'y_vector': '25', 'base_area': 'Tokyo Area 2', 'extraction_area': 'Park', 'map_code': 'MAP002', 'prefectures': 'Tokyo', 'municipalities': 'Shinjuku'},
        {'id': 'PRA003', 'latitude': 35.6612, 'longitude': 139.7471, 'x_vector': '12', 'y_vector': '22', 'base_area': 'Tokyo Area 3', 'extraction_area': 'Hospital', 'map_code': 'MAP003', 'prefectures': 'Tokyo', 'municipalities': 'Shibuya'},
        {'id': 'PRA004', 'latitude': 35.6512, 'longitude': 139.7371, 'x_vector': '18', 'y_vector': '28', 'base_area': 'Tokyo Area 4', 'extraction_area': 'School', 'map_code': 'MAP004', 'prefectures': 'Tokyo', 'municipalities': 'Minato'},
        {'id': 'PRA005', 'latitude': 35.6412, 'longitude': 139.7271, 'x_vector': '14', 'y_vector': '24', 'base_area': 'Tokyo Area 5', 'extraction_area': 'Office', 'map_code': 'MAP005', 'prefectures': 'Tokyo', 'municipalities': 'Ota'},
    ]
    
    for data in prohibit_data:
        obj, created = ProhibitedApproachInfo.objects.get_or_create(
            id=data['id'],
            defaults={
                'latitude': data['latitude'],
                'longitude': data['longitude'],
                'x_vector': data['x_vector'],
                'y_vector': data['y_vector'],
                'base_area': data['base_area'],
                'extraction_area': data['extraction_area'],
                'map_code': data['map_code'],
                'prefectures': data['prefectures'],
                'municipalities': data['municipalities'],
                'created_at': '2025-05-06 10:00:00',
                'updated_at': '2025-05-06 10:00:00'
            }
        )
        print(f"{'Created' if created else 'Found'} prohibited approach info: {obj.id}")

    # 10. Create SettingsInfo objects
    print("\nCreating Settings Info...")
    settings_data = [
        {'id': 'SI001', 'key_name': 'refresh_interval', 'value': '300', 'summary': 'UI refresh interval in seconds'},
        {'id': 'SI002', 'key_name': 'map_zoom_level', 'value': '15', 'summary': 'Default map zoom level'},
        {'id': 'SI003', 'key_name': 'alert_sound', 'value': 'enable', 'summary': 'Enable alert sounds'},
        {'id': 'SI004', 'key_name': 'alert_vibration', 'value': 'enable', 'summary': 'Enable alert vibration'},
        {'id': 'SI005', 'key_name': 'language', 'value': 'ja', 'summary': 'UI language'},
    ]
    
    for data in settings_data:
        obj, created = SettingsInfo.objects.get_or_create(
            id=data['id'],
            defaults={
                'key_name': data['key_name'],
                'value': data['value'],
                'summary': data['summary'],
                'created_at': '2025-05-07 10:00:00',
                'updated_at': '2025-05-07 10:00:00'
            }
        )
        print(f"{'Created' if created else 'Found'} settings info: {obj.key_name}")

def run_all_demos():
    """Run all demo data creation functions"""
    print("Creating all demo data...\n")
    
    # Create basic user management data
    setup_roles()
    create_users()
    
    # Create LIPS app data
    create_lips_data()
    
    print("\nAll demo data has been created successfully!")

if __name__ == '__main__':
    run_all_demos()