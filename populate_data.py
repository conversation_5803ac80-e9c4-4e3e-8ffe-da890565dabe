#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to populate the database with initial data.
This script will be called from start.sh to ensure the database has the necessary data.
"""

import os
import sys
import django
import datetime
from django.utils import timezone
from django.db import transaction

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Import models after Django setup
from lips.models import (
    AlertInstructionManagementInfo,
    DeviceMaster,
    DeviceReceiveData,
    FixInfo,
    HistoricalJudgeAlertInstruction,
    PermitedApproachInfo,
    ProhibitedApproachInfo,
    SettingsInfo,
    UserMaster,
    WorkInfo
)
from usermanagement.models import Role, User
from django.contrib.auth.hashers import make_password

def create_roles():
    """Create default roles if they don't exist."""
    print("Creating roles...")
    roles = ['admin', 'manager', 'user']
    for i, role_name in enumerate(roles, 1):
        # First check if the role exists by name
        existing_role = Role.objects.filter(name=role_name).first()
        if existing_role:
            print(f"Role already exists: {role_name}")
            continue

        # Then try to create it with the specified ID
        try:
            role = Role.objects.create(id=i, name=role_name)
            print(f"Created role: {role_name}")
        except Exception as e:
            print(f"Error creating role {role_name}: {e}")
            # Try without specifying the ID
            try:
                role = Role.objects.create(name=role_name)
                print(f"Created role with auto-generated ID: {role_name}")
            except Exception as e:
                print(f"Failed to create role {role_name}: {e}")

def create_admin_user():
    """Create an admin user if one doesn't exist."""
    print("Checking for admin user...")
    if not User.objects.filter(role__name='admin').exists():
        print("Creating admin user...")
        admin_role = Role.objects.get(name='admin')
        User.objects.create(
            username='admin',
            password=make_password('admin'),
            email='<EMAIL>',
            role=admin_role,
            full_name='Admin User',
            status='active'
        )
        print("Admin user created successfully")
    else:
        print("Admin user already exists")

def create_user_masters():
    """Create user masters if they don't exist."""
    print("Creating user masters...")
    user_ids = ['USER001', 'USER002', 'USER003', 'USER004', 'USER005']
    for user_id in user_ids:
        user, created = UserMaster.objects.get_or_create(
            id=user_id,
            defaults={
                'created_at': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                'updated_at': timezone.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        )
        if created:
            print(f"Created user master: {user_id}")
        else:
            print(f"User master already exists: {user_id}")

def create_work_info():
    """Create work info if it doesn't exist."""
    print("Creating work info...")
    work_data = [
        {'work_name': 'SVLR0008', 'group_num': '1111', 'user_id': 'USER001'},
        {'work_name': 'SVLR0010', 'group_num': '1112', 'user_id': 'USER002'},
        {'work_name': 'SVLR0012', 'group_num': '2111', 'user_id': 'USER003'},
        {'work_name': 'SVLR0015', 'group_num': '3111', 'user_id': 'USER001'},
        {'work_name': 'SVLR0018', 'group_num': '4111', 'user_id': 'USER002'},
    ]

    for i, data in enumerate(work_data, 1):
        try:
            user = UserMaster.objects.get(id=data['user_id'])
            work, created = WorkInfo.objects.get_or_create(
                id=i,
                defaults={
                    'user_id': user,
                    'work_name': data['work_name'],
                    'group_num': data['group_num'],
                    'created_at': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'updated_at': timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            )
            if created:
                print(f"Created work info: {data['work_name']}")
            else:
                print(f"Work info already exists: {data['work_name']}")
        except UserMaster.DoesNotExist:
            print(f"User master {data['user_id']} does not exist. Skipping work info creation.")

def create_device_masters():
    """Create device masters if they don't exist."""
    print("Creating device masters...")
    device_data = [
        {'id': 1, 'user_id': 'USER001', 'display_device_id': '0010', 'device_name': 'motomachi', 'work_id': 1, 'work_time': '02:30:00', 'battery': 85, 'previous_alert_instruction': '0', 'signal_period': 30, 'status': 'normal'},
        {'id': 2, 'user_id': 'USER002', 'display_device_id': '0020', 'device_name': 'shibuya', 'work_id': 2, 'work_time': '03:45:00', 'battery': 65, 'previous_alert_instruction': '0', 'signal_period': 45, 'status': 'normal'},
        {'id': 3, 'user_id': 'USER003', 'display_device_id': '0030', 'device_name': 'akihabara', 'work_id': 3, 'work_time': '05:20:00', 'battery': 25, 'previous_alert_instruction': '1', 'signal_period': 60, 'status': 'warning'},
        {'id': 4, 'user_id': 'USER001', 'display_device_id': '0040', 'device_name': 'shinjuku', 'work_id': 4, 'work_time': '01:10:00', 'battery': 10, 'previous_alert_instruction': '1', 'signal_period': 30, 'status': 'error'},
        {'id': 5, 'user_id': 'USER002', 'display_device_id': '0050', 'device_name': 'tokyo', 'work_id': 5, 'work_time': '00:45:00', 'battery': 90, 'previous_alert_instruction': '0', 'signal_period': 45, 'status': 'normal'},
    ]

    for data in device_data:
        try:
            user = UserMaster.objects.get(id=data['user_id'])
            work = WorkInfo.objects.get(id=data['work_id'])

            device, created = DeviceMaster.objects.get_or_create(
                id=data['id'],
                defaults={
                    'user_id': user,
                    'display_device_id': data['display_device_id'],
                    'device_name': data['device_name'],
                    'work_id': work,
                    'work_time': data['work_time'],
                    'battery': data['battery'],
                    'previous_alert_instruction': data['previous_alert_instruction'],
                    'signal_period': data['signal_period'],
                    'status': data['status'],
                    'created_at': timezone.now(),
                    'updated_at': timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            )
            if created:
                print(f"Created device master: {data['device_name']}")
            else:
                print(f"Device master already exists: {data['device_name']}")
        except (UserMaster.DoesNotExist, WorkInfo.DoesNotExist) as e:
            print(f"Error creating device master {data['device_name']}: {e}")

def create_settings_info():
    """Create settings info if it doesn't exist."""
    print("Creating settings info...")
    settings_data = [
        {'id': 'SI001', 'key_name': 'refresh_interval', 'value': '300', 'summary': 'UI refresh interval in seconds'},
        {'id': 'SI002', 'key_name': 'map_zoom_level', 'value': '15', 'summary': 'Default map zoom level'},
        {'id': 'SI003', 'key_name': 'alert_sound', 'value': 'enable', 'summary': 'Enable alert sounds'},
        {'id': 'SI004', 'key_name': 'alert_vibration', 'value': 'enable', 'summary': 'Enable alert vibration'},
        {'id': 'SI005', 'key_name': 'language', 'value': 'ja', 'summary': 'UI language'},
    ]

    for data in settings_data:
        try:
            setting, created = SettingsInfo.objects.get_or_create(
                id=data['id'],
                defaults={
                    'key_name': data['key_name'],
                    'value': data['value'],
                    'summary': data['summary'],
                    'created_at': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'updated_at': timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            )
            if created:
                print(f"Created settings info: {data['key_name']}")
            else:
                print(f"Settings info already exists: {data['key_name']}")
        except Exception as e:
            print(f"Error creating setting {data['key_name']}: {e}")

    # Create user-specific settings
    try:
        # Import and run the populate_settings script
        import populate_settings
        populate_settings.populate_default_settings()
    except Exception as e:
        print(f"Error running populate_settings: {e}")

def create_fix_info():
    """Create fix info if it doesn't exist."""
    print("Creating fix info...")
    fix_info_data = [
        {'id': 'FI001', 'key_name': 'approach_threshold', 'val': '5.0', 'summary': 'Approach distance threshold'},
        {'id': 'FI002', 'key_name': 'battery_threshold', 'val': '20', 'summary': 'Battery warning threshold'},
        {'id': 'FI003', 'key_name': 'signal_interval', 'val': '30', 'summary': 'Signal interval in seconds'},
        {'id': 'FI004', 'key_name': 'offline_timeout', 'val': '300', 'summary': 'Offline timeout in seconds'},
        {'id': 'FI005', 'key_name': 'alert_expiry', 'val': '3600', 'summary': 'Alert expiry in seconds'},
    ]

    for data in fix_info_data:
        fix_info, created = FixInfo.objects.get_or_create(
            id=data['id'],
            defaults={
                'key_name': data['key_name'],
                'val': data['val'],
                'summary': data['summary'],
                'created_at': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                'updated_at': timezone.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        )
        if created:
            print(f"Created fix info: {data['key_name']}")
        else:
            print(f"Fix info already exists: {data['key_name']}")

def create_alert_instruction_management_info():
    """Create alert instruction management info if it doesn't exist."""
    print("Creating alert instruction management info...")
    for i in range(1, 6):
        info, created = AlertInstructionManagementInfo.objects.get_or_create(id=i)
        if created:
            print(f"Created alert instruction info with ID: {i}")
        else:
            print(f"Alert instruction info already exists with ID: {i}")

def create_device_receive_data():
    """Create device receive data if it doesn't exist."""
    print("Creating device receive data...")
    receive_data = [
        {'id': 'DRD001', 'device_id': 1, 'time': '2025-04-29 09:00:00', 'previous_alert_id': 'ALERT001', 'battery': 85, 'dop': 5, 'ns_latitude_identifier': 'N', 'latitude': 356812, 'ew_longitude_identifier': 'E', 'longitude': 1397671, 'x_acceleration': 100, 'y_acceleration': 200, 'z_acceleration': 300, 'alive_count': 1},
        {'id': 'DRD002', 'device_id': 2, 'time': '2025-04-29 09:15:00', 'previous_alert_id': 'ALERT002', 'battery': 65, 'dop': 7, 'ns_latitude_identifier': 'N', 'latitude': 356732, 'ew_longitude_identifier': 'E', 'longitude': 1397601, 'x_acceleration': 120, 'y_acceleration': 220, 'z_acceleration': 320, 'alive_count': 2},
        {'id': 'DRD003', 'device_id': 3, 'time': '2025-04-29 09:30:00', 'previous_alert_id': 'ALERT003', 'battery': 25, 'dop': 6, 'ns_latitude_identifier': 'N', 'latitude': 356602, 'ew_longitude_identifier': 'E', 'longitude': 1397521, 'x_acceleration': 110, 'y_acceleration': 210, 'z_acceleration': 310, 'alive_count': 3},
        {'id': 'DRD004', 'device_id': 4, 'time': '2025-04-29 09:45:00', 'previous_alert_id': 'ALERT004', 'battery': 10, 'dop': 8, 'ns_latitude_identifier': 'N', 'latitude': 356502, 'ew_longitude_identifier': 'E', 'longitude': 1397421, 'x_acceleration': 130, 'y_acceleration': 230, 'z_acceleration': 330, 'alive_count': 4},
        {'id': 'DRD005', 'device_id': 5, 'time': '2025-04-29 10:00:00', 'previous_alert_id': 'ALERT005', 'battery': 90, 'dop': 4, 'ns_latitude_identifier': 'N', 'latitude': 356402, 'ew_longitude_identifier': 'E', 'longitude': 1397321, 'x_acceleration': 90, 'y_acceleration': 190, 'z_acceleration': 290, 'alive_count': 5},
    ]

    for data in receive_data:
        try:
            device = DeviceMaster.objects.get(id=data['device_id'])
            obj, created = DeviceReceiveData.objects.get_or_create(
                id=data['id'],
                defaults={
                    'device_id': device,
                    'time': data['time'],
                    'previous_alert_id': data['previous_alert_id'],
                    'battery': data['battery'],
                    'dop': data['dop'],
                    'ns_latitude_identifier': data['ns_latitude_identifier'],
                    'latitude': data['latitude'],
                    'ew_longitude_identifier': data['ew_longitude_identifier'],
                    'longitude': data['longitude'],
                    'x_acceleration': data['x_acceleration'],
                    'y_acceleration': data['y_acceleration'],
                    'z_acceleration': data['z_acceleration'],
                    'alive_count': data['alive_count'],
                    'created_at': data['time'],
                    'updated_at': data['time']
                }
            )
            if created:
                print(f"Created device receive data: {data['id']}")
            else:
                print(f"Device receive data already exists: {data['id']}")
        except DeviceMaster.DoesNotExist:
            print(f"Device master {data['device_id']} does not exist. Skipping device receive data creation.")

def create_historical_judge_alert_instruction():
    """Create historical judge alert instruction if it doesn't exist."""
    print("Creating historical judge alert instruction...")
    hist_data = [
        {'id': 'HJA001', 'device_id': 1, 'ns_latitude_identifier': 'N', 'latitude': 356812, 'ew_longitude_identifier': 'E', 'longitude': 1397671, 'utm_x': '539750', 'utm_y': '3938100', 'x_acceleration': 100, 'y_acceleration': 200, 'z_acceleration': 300, 'alert_instruction': 0, 'alert_id': 'ALERT001', 'previous_alert_id': 'PREV001', 'work_time': '02:30:00', 'group_num': '1111', 'alive_count': 1},
        {'id': 'HJA002', 'device_id': 2, 'ns_latitude_identifier': 'N', 'latitude': 356732, 'ew_longitude_identifier': 'E', 'longitude': 1397601, 'utm_x': '539700', 'utm_y': '3938050', 'x_acceleration': 120, 'y_acceleration': 220, 'z_acceleration': 320, 'alert_instruction': 1, 'alert_id': 'ALERT002', 'previous_alert_id': 'PREV002', 'work_time': '03:45:00', 'group_num': '1112', 'alive_count': 2},
        {'id': 'HJA003', 'device_id': 3, 'ns_latitude_identifier': 'N', 'latitude': 356602, 'ew_longitude_identifier': 'E', 'longitude': 1397521, 'utm_x': '539650', 'utm_y': '3938000', 'x_acceleration': 110, 'y_acceleration': 210, 'z_acceleration': 310, 'alert_instruction': 1, 'alert_id': 'ALERT003', 'previous_alert_id': 'PREV003', 'work_time': '05:20:00', 'group_num': '2111', 'alive_count': 3},
        {'id': 'HJA004', 'device_id': 4, 'ns_latitude_identifier': 'N', 'latitude': 356502, 'ew_longitude_identifier': 'E', 'longitude': 1397421, 'utm_x': '539600', 'utm_y': '3937950', 'x_acceleration': 130, 'y_acceleration': 230, 'z_acceleration': 330, 'alert_instruction': 0, 'alert_id': 'ALERT004', 'previous_alert_id': 'PREV004', 'work_time': '01:10:00', 'group_num': '3111', 'alive_count': 4},
        {'id': 'HJA005', 'device_id': 5, 'ns_latitude_identifier': 'N', 'latitude': 356402, 'ew_longitude_identifier': 'E', 'longitude': 1397321, 'utm_x': '539550', 'utm_y': '3937900', 'x_acceleration': 90, 'y_acceleration': 190, 'z_acceleration': 290, 'alert_instruction': 0, 'alert_id': 'ALERT005', 'previous_alert_id': 'PREV005', 'work_time': '00:45:00', 'group_num': '4111', 'alive_count': 5},
    ]

    for data in hist_data:
        try:
            device = DeviceMaster.objects.get(id=data['device_id'])
            obj, created = HistoricalJudgeAlertInstruction.objects.get_or_create(
                id=data['id'],
                defaults={
                    'device_id': device,
                    'ns_latitude_identifier': data['ns_latitude_identifier'],
                    'latitude': data['latitude'],
                    'ew_longitude_identifier': data['ew_longitude_identifier'],
                    'longitude': data['longitude'],
                    'utm_x': data['utm_x'],
                    'utm_y': data['utm_y'],
                    'x_acceleration': data['x_acceleration'],
                    'y_acceleration': data['y_acceleration'],
                    'z_acceleration': data['z_acceleration'],
                    'alert_instruction': data['alert_instruction'],
                    'alert_id': data['alert_id'],
                    'previous_alert_id': data['previous_alert_id'],
                    'work_time': data['work_time'],
                    'group_num': data['group_num'],
                    'alive_count': data['alive_count'],
                    'created_at': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'updated_at': timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            )
            if created:
                print(f"Created historical alert: {data['id']}")
            else:
                print(f"Historical alert already exists: {data['id']}")
        except DeviceMaster.DoesNotExist:
            print(f"Device master {data['device_id']} does not exist. Skipping historical alert creation.")

def create_permitted_approach_info():
    """Create permitted approach info if it doesn't exist."""
    print("Creating permitted approach info...")
    permit_data = [
        {'id': 'PA001', 'area_element_num': 1, 'work_id': 1, 'area_info': '{"type":"Polygon","coordinates":[[[139.7671,35.6812],[139.7701,35.6832],[139.7721,35.6802],[139.7691,35.6782],[139.7671,35.6812]]]}'},
        {'id': 'PA002', 'area_element_num': 2, 'work_id': 2, 'area_info': '{"type":"Polygon","coordinates":[[[139.7571,35.6712],[139.7601,35.6732],[139.7621,35.6702],[139.7591,35.6682],[139.7571,35.6712]]]}'},
        {'id': 'PA003', 'area_element_num': 3, 'work_id': 3, 'area_info': '{"type":"Polygon","coordinates":[[[139.7471,35.6612],[139.7501,35.6632],[139.7521,35.6602],[139.7491,35.6582],[139.7471,35.6612]]]}'},
        {'id': 'PA004', 'area_element_num': 4, 'work_id': 4, 'area_info': '{"type":"Polygon","coordinates":[[[139.7371,35.6512],[139.7401,35.6532],[139.7421,35.6502],[139.7391,35.6482],[139.7371,35.6512]]]}'},
        {'id': 'PA005', 'area_element_num': 5, 'work_id': 5, 'area_info': '{"type":"Polygon","coordinates":[[[139.7271,35.6412],[139.7301,35.6432],[139.7321,35.6402],[139.7291,35.6382],[139.7271,35.6412]]]}'},
    ]

    for data in permit_data:
        try:
            work = WorkInfo.objects.get(id=data['work_id'])
            obj, created = PermitedApproachInfo.objects.get_or_create(
                id=data['id'],
                defaults={
                    'area_element_num': data['area_element_num'],
                    'work_id': work,
                    'area_info': data['area_info'],
                    'created_at': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'updated_at': timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            )
            if created:
                print(f"Created permitted approach info: {data['id']}")
            else:
                print(f"Permitted approach info already exists: {data['id']}")
        except WorkInfo.DoesNotExist:
            print(f"Work info {data['work_id']} does not exist. Skipping permitted approach info creation.")

def create_prohibited_approach_info():
    """Create prohibited approach info if it doesn't exist."""
    print("Creating prohibited approach info...")
    prohibit_data = [
        {'id': 'PRA001', 'latitude': 35.6812, 'longitude': 139.7671, 'x_vector': '10', 'y_vector': '20', 'base_area': 'Tokyo Area 1', 'extraction_area': 'Station', 'map_code': 'MAP001', 'prefectures': 'Tokyo', 'municipalities': 'Chiyoda'},
        {'id': 'PRA002', 'latitude': 35.6712, 'longitude': 139.7571, 'x_vector': '15', 'y_vector': '25', 'base_area': 'Tokyo Area 2', 'extraction_area': 'Park', 'map_code': 'MAP002', 'prefectures': 'Tokyo', 'municipalities': 'Shinjuku'},
        {'id': 'PRA003', 'latitude': 35.6612, 'longitude': 139.7471, 'x_vector': '12', 'y_vector': '22', 'base_area': 'Tokyo Area 3', 'extraction_area': 'Hospital', 'map_code': 'MAP003', 'prefectures': 'Tokyo', 'municipalities': 'Shibuya'},
        {'id': 'PRA004', 'latitude': 35.6512, 'longitude': 139.7371, 'x_vector': '18', 'y_vector': '28', 'base_area': 'Tokyo Area 4', 'extraction_area': 'School', 'map_code': 'MAP004', 'prefectures': 'Tokyo', 'municipalities': 'Minato'},
        {'id': 'PRA005', 'latitude': 35.6412, 'longitude': 139.7271, 'x_vector': '14', 'y_vector': '24', 'base_area': 'Tokyo Area 5', 'extraction_area': 'Office', 'map_code': 'MAP005', 'prefectures': 'Tokyo', 'municipalities': 'Ota'},
    ]

    for data in prohibit_data:
        obj, created = ProhibitedApproachInfo.objects.get_or_create(
            id=data['id'],
            defaults={
                'latitude': data['latitude'],
                'longitude': data['longitude'],
                'x_vector': data['x_vector'],
                'y_vector': data['y_vector'],
                'base_area': data['base_area'],
                'extraction_area': data['extraction_area'],
                'map_code': data['map_code'],
                'prefectures': data['prefectures'],
                'municipalities': data['municipalities'],
                'created_at': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                'updated_at': timezone.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        )
        if created:
            print(f"Created prohibited approach info: {data['id']}")
        else:
            print(f"Prohibited approach info already exists: {data['id']}")

def create_additional_users():
    """Add additional users to the user table if they don't exist."""
    print("Adding additional users...")
    additional_users = [
        {
            'id': 5,
            'username': 'user1',
            'password': make_password('password1'),
            'email': '<EMAIL>',
            'full_name': 'User One',
            'status': 'active',
            'phone_number': '************',
            'company_name': 'Notification Alert User',
            'role_name': 'user'
        },
        {
            'id': 7,
            'username': 'towhid12',
            'password': make_password('password2'),
            'email': '<EMAIL>',
            'full_name': 'towhid',
            'status': 'active',
            'phone_number': '1223344',
            'company_name': 'itage it',
            'role_name': 'admin'
        },
        {
            'id': 8,
            'username': 'user',
            'password': make_password('password3'),
            'email': '<EMAIL>',
            'full_name': 'user',
            'status': 'active',
            'phone_number': '018190734334',
            'company_name': 'itage',
            'role_name': 'user'
        },
        {
            'id': 9,
            'username': 'manager',
            'password': make_password('password4'),
            'email': '<EMAIL>',
            'full_name': 'manager',
            'status': 'active',
            'phone_number': '018190734334',
            'company_name': 'itage',
            'role_name': 'manager'
        },
        {
            'id': 11,
            'username': 'user123',
            'password': make_password('password5'),
            'email': '<EMAIL>',
            'full_name': 'user123',
            'status': 'active',
            'phone_number': '018190734334',
            'company_name': 'itage',
            'role_name': 'user'
        },
        {
            'id': 12,
            'username': 'manager1',
            'password': make_password('password6'),
            'email': '<EMAIL>',
            'full_name': 'manager',
            'status': 'active',
            'phone_number': '01822223933',
            'company_name': 'itage',
            'role_name': 'manager'
        }
    ]

    for user_data in additional_users:
        try:
            role = Role.objects.get(name=user_data['role_name'])
            user, created = User.objects.get_or_create(
                id=user_data['id'],
                defaults={
                    'username': user_data['username'],
                    'password': user_data['password'],
                    'email': user_data['email'],
                    'full_name': user_data['full_name'],
                    'status': user_data['status'],
                    'phone_number': user_data['phone_number'],
                    'company_name': user_data['company_name'],
                    'role': role
                }
            )
            if created:
                print(f"Created user: {user_data['username']}")
            else:
                print(f"User already exists: {user_data['username']}")
        except Role.DoesNotExist:
            print(f"Role {user_data['role_name']} does not exist. Skipping user creation for {user_data['username']}.")

def main():
    """Main function to populate the database with initial data."""
    print("Starting database population...")

    try:
        with transaction.atomic():
            # Create roles and admin user
            create_roles()
            create_admin_user()

            # Create lips app data
            create_user_masters()
            create_work_info()
            create_device_masters()
            create_settings_info()
            create_fix_info()
            create_alert_instruction_management_info()

            # Create additional data
            create_device_receive_data()
            create_historical_judge_alert_instruction()
            create_permitted_approach_info()
            create_prohibited_approach_info()
            create_additional_users()

        print("Database population completed successfully!")
    except Exception as e:
        print(f"Error populating database: {e}")
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
