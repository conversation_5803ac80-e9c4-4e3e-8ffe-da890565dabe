#!/usr/bin/env python
"""
Script to reset and reapply migrations for the lips app.
This script will:
1. Fake revert all migrations for the lips app
2. Apply migrations up to the merge migration
3. Apply the fix migration
4. Apply remaining migrations
"""

import os
import sys
import subprocess
import argparse

def run_command(command, description=None):
    """Run a shell command and print its output"""
    if description:
        print(f"\n{description}...")

    print(f"Running: {' '.join(command)}")
    result = subprocess.run(command, capture_output=True, text=True)

    if result.returncode != 0:
        print(f"Error: {result.stderr}")
        return False

    print(result.stdout)
    return True

def main():
    parser = argparse.ArgumentParser(description='Reset and reapply migrations for the lips app')
    parser.add_argument('--fake', action='store_true', help='Use --fake flag with migrations')
    parser.add_argument('--skip-zero', action='store_true', help='Skip the migrate zero step')
    args = parser.parse_args()

    if not args.skip_zero:
        # 1. Fake revert all migrations for the lips app
        fake_zero_cmd = ["python", "manage.py", "migrate", "lips", "zero"]
        if args.fake:
            fake_zero_cmd.append("--fake")

        success = run_command(
            fake_zero_cmd,
            "Reverting all migrations for the lips app"
        )
        if not success:
            print("Failed to revert migrations. Exiting.")
            return 1

    # 2. Apply migrations up to the merge migration
    migrate_to_merge_cmd = ["python", "manage.py", "migrate", "lips", "0013_merge_20250517_0742"]
    if args.fake:
        migrate_to_merge_cmd.append("--fake")

    success = run_command(
        migrate_to_merge_cmd,
        "Applying migrations up to the merge migration"
    )
    if not success:
        print("Failed to apply migrations up to merge. Exiting.")
        return 1

    # 3. Apply the fix migration - skip this step as it's now part of the merge
    print("\nSkipping fix migration as it's now part of the merge...")
    success = True

    # 4. Apply remaining migrations
    migrate_cmd = ["python", "manage.py", "migrate", "lips"]
    if args.fake:
        migrate_cmd.append("--fake")

    success = run_command(
        migrate_cmd,
        "Applying remaining migrations"
    )
    if not success:
        print("Failed to apply remaining migrations. Exiting.")
        return 1

    print("\nMigration reset and reapplication completed successfully!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
