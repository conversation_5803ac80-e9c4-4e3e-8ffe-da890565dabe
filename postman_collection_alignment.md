# Postman Collection Alignment

This document outlines the changes needed to align our API implementation with the Postman collection.

## Authentication Endpoints

The Postman collection expects authentication endpoints at `/api/v1/auth/...`, but our implementation has them at `/userapi/...`. We've addressed this by:

1. Creating a new `auth.py` file in the usermanagement app that maps the authentication endpoints
2. Including these endpoints at `/api/v1/auth/` in the main URLs configuration

## Device Settings Endpoint

The Postman collection expects device settings at `/devices/settings`, but our implementation had them at `/device-settings/`. We've addressed this by:

1. Changing the router registration to use `devices/settings` as the base name
2. Adding a basename parameter to avoid URL conflicts

## Work Details Endpoint

The Postman collection uses `/works/:workName` for work details, but our implementation used `/works/{id}/details/`. We've addressed this by:

1. Setting `lookup_field = 'work_name'` in the WorkViewSet
2. Overriding the `retrieve` method to handle work details retrieval

## Testing the API

To test the API with the Postman collection:

1. Update the `baseUrl` variable in the Postman collection to point to your local server (e.g., `http://localhost:8000/api/v1`)
2. Run the Django server with `python manage.py runserver`
3. Import the Postman collection and run the requests

## Remaining Issues

Some endpoints in the Postman collection may still have minor differences compared to our implementation:

1. Field naming conventions (camelCase vs. snake_case)
2. Response structure differences
3. Authentication token format differences

These issues can be addressed by:

1. Adding serializer methods to transform field names
2. Customizing response formats in the views
3. Ensuring the authentication token format matches the expected format in the Postman collection
