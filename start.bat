@echo off
echo ===================================
echo LIPS Web Application Starter
echo ===================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed or not in PATH.
    echo Please install Python 3.8 or higher from https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

REM Check Python version
for /f "tokens=2" %%I in ('python --version 2^>^&1') do set PYTHON_VERSION=%%I
for /f "tokens=1,2 delims=." %%a in ("%PYTHON_VERSION%") do (
    set PYTHON_MAJOR=%%a
    set PYTHON_MINOR=%%b
)

if %PYTHON_MAJOR% LSS 3 (
    echo Python 3.8 or higher is required. You have Python %PYTHON_VERSION%
    pause
    exit /b 1
)

if %PYTHON_MAJOR% EQU 3 (
    if %PYTHON_MINOR% LSS 8 (
        echo Python 3.8 or higher is required. You have Python %PYTHON_VERSION%
        pause
        exit /b 1
    )
)

echo Python %PYTHON_VERSION% detected

REM Check if pip is installed
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo pip is not installed.
    echo Please install pip for Python.
    echo.
    pause
    exit /b 1
)

echo pip is installed

REM Check if MySQL is installed
mysql --version >nul 2>&1
if %errorlevel% equ 0 (
    echo MySQL is installed
) else (
    echo MySQL might not be installed
    echo You'll need to install MySQL and create a database named 'lipsweb'
    set /p CONTINUE_WITHOUT_MYSQL="Do you want to continue anyway? (y/n): "
    if /i not "%CONTINUE_WITHOUT_MYSQL%"=="y" (
        exit /b 1
    )
)

REM Create virtual environment if it doesn't exist
if not exist venv (
    echo Creating virtual environment...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo Failed to create virtual environment.
        pause
        exit /b 1
    )
    echo Virtual environment created
) else (
    echo Virtual environment already exists
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo Failed to activate virtual environment.
    pause
    exit /b 1
)
echo Virtual environment activated

REM Install dependencies
echo Installing dependencies...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo Failed to install dependencies from requirements.txt.
    pause
    exit /b 1
)

REM Explicitly install PyMySQL
echo Installing PyMySQL...
pip install PyMySQL
if %errorlevel% neq 0 (
    echo Failed to install PyMySQL.
    pause
    exit /b 1
)
echo Dependencies installed

REM Create .env file if it doesn't exist
if not exist .env (
    echo Creating .env file...

    REM Generate a random secret key
    for /f "tokens=*" %%a in ('python -c "import random; import string; print(''.join(random.choice(string.ascii_letters + string.digits + string.punctuation) for _ in range(50)))"') do set SECRET_KEY=%%a

    REM Get database configuration
    echo Please provide your MySQL database configuration:
    set /p DB_NAME="Database name (default: lipsweb): "
    if "%DB_NAME%"=="" set DB_NAME=lipsweb

    set /p DB_USER="Database user (default: root): "
    if "%DB_USER%"=="" set DB_USER=root

    set /p DB_PASSWORD="Database password: "

    set /p DB_HOST="Database host (default: localhost): "
    if "%DB_HOST%"=="" set DB_HOST=localhost

    set /p DB_PORT="Database port (default: 3306): "
    if "%DB_PORT%"=="" set DB_PORT=3306

    REM Create .env file
    echo DJANGO_SECRET_KEY=%SECRET_KEY%> .env
    echo DEBUG=True>> .env
    echo DB_NAME=%DB_NAME%>> .env
    echo DB_USER=%DB_USER%>> .env
    echo DB_PASSWORD=%DB_PASSWORD%>> .env
    echo DB_HOST=%DB_HOST%>> .env
    echo DB_PORT=%DB_PORT%>> .env

    echo .env file created
) else (
    echo .env file already exists
)

REM Create database if it doesn't exist
echo Checking if database exists...
for /f "tokens=2 delims==" %%a in ('findstr "DB_NAME" .env') do set DB_NAME=%%a
for /f "tokens=2 delims==" %%a in ('findstr "DB_USER" .env') do set DB_USER=%%a
for /f "tokens=2 delims==" %%a in ('findstr "DB_PASSWORD" .env') do set DB_PASSWORD=%%a
for /f "tokens=2 delims==" %%a in ('findstr "DB_HOST" .env') do set DB_HOST=%%a
for /f "tokens=2 delims==" %%a in ('findstr "DB_PORT" .env') do set DB_PORT=%%a

mysql --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Attempting to create database if it doesn't exist...

    REM Create database creation SQL
    echo CREATE DATABASE IF NOT EXISTS %DB_NAME% CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; > create_db.sql

    REM Try to create the database
    if "%DB_PASSWORD%"=="" (
        REM No password
        mysql -u %DB_USER% -h %DB_HOST% -P %DB_PORT% < create_db.sql
    ) else (
        REM With password
        mysql -u %DB_USER% -p%DB_PASSWORD% -h %DB_HOST% -P %DB_PORT% < create_db.sql
    )

    if %errorlevel% equ 0 (
        echo Database checked/created successfully
        del create_db.sql
    ) else (
        echo Failed to create database. You may need to create it manually:
        echo mysql -u %DB_USER% -p
        echo CREATE DATABASE %DB_NAME% CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
        set /p CONTINUE_WITHOUT_DB="Do you want to continue anyway? (y/n): "
        if /i not "%CONTINUE_WITHOUT_DB%"=="y" (
            call deactivate
            del create_db.sql 2>nul
            pause
            exit /b 1
        )
        del create_db.sql 2>nul
    )
) else (
    echo MySQL command not found. Cannot create database automatically.
    echo You may need to create the database manually before proceeding.
)

REM Run migrations
echo Running database migrations...
python manage.py migrate
if %errorlevel% neq 0 (
    echo Failed to run migrations.
    echo This might be due to database connection issues.
    set /p CONTINUE_WITHOUT_MIGRATIONS="Do you want to continue anyway? (y/n): "
    if /i not "%CONTINUE_WITHOUT_MIGRATIONS%"=="y" (
        call deactivate
        pause
        exit /b 1
    )
) else (
    echo Migrations completed
)

REM Populate database with initial data
echo Populating database with initial data...
python populate_data.py
if %errorlevel% neq 0 (
    echo Failed to populate database with initial data.
    set /p CONTINUE_WITHOUT_DATA="Do you want to continue anyway? (y/n): "
    if /i not "%CONTINUE_WITHOUT_DATA%"=="y" (
        call deactivate
        pause
        exit /b 1
    )
) else (
    echo Database populated successfully
)

REM Generate token
echo Generating access token...
python generate_token.py
if %errorlevel% neq 0 (
    echo Failed to generate token, but this is not critical.
)

REM Get port number if provided
set PORT=8000
if not "%~1"=="" set PORT=%~1

REM Start server
echo.
echo === Starting Django Server ===
echo Setup completed successfully!
echo Starting the Django development server on port %PORT%...
echo Press Ctrl+C to stop the server
echo.

python manage.py runserver 0.0.0.0:%PORT%

REM Deactivate virtual environment when server stops
call deactivate

echo Server stopped
pause
