from django.urls import include, path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from .views import (UserProfileView,
                    DeviceViewSet, DeviceSettingsViewSet,
                    WorkViewSet, AlertViewSet)

# Router for API endpoints using LIPS models
router = DefaultRouter()
router.register(r'devices', DeviceViewSet, basename='devices')
router.register(r'devices-settings', DeviceSettingsViewSet, basename='device-settings')
router.register(r'works', WorkViewSet, basename='works')
router.register(r'alerts', AlertViewSet, basename='alerts')

urlpatterns = [
    # User endpoints
    path('users/me/', UserProfileView.as_view(), name='user-profile'),

    # Include router URLs
    path('', include(router.urls)),
]
