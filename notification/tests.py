from django.test import TestCase, Client
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model
from usermanagement.models import Role
from lips.models import DeviceMaster, WorkInfo, HistoricalJudgeAlertInstruction
import json

User = get_user_model()

class NotificationAPITestCase(TestCase):
    """Test case for the notification API endpoints using LIPS models"""

    def setUp(self):
        """Set up test data"""
        # Create roles
        self.admin_role = Role.objects.create(name='admin')
        self.manager_role = Role.objects.create(name='manager')

        # Create users
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='password123',
            role=self.admin_role
        )

        self.manager_user = User.objects.create_user(
            username='manager',
            email='<EMAIL>',
            password='password123',
            role=self.manager_role
        )

        # Create test data
        self.device = DeviceMaster.objects.create(
            id='TEST001',
            user_id='USER001',
            display_device_id='0001',
            device_name='Test Device',
            work_id='WORK001',
            work_time='01:30:00',
            battery=75,
            previous_alert_instruction='0',
            signal_period=30,
            created_at='2025-05-01 10:00:00',
            updated_at='2025-05-01 10:00:00'
        )

        self.work = WorkInfo.objects.create(
            id='WORK001',
            user_id='USER001',
            work_name='WORK001',
            group_num='1111',
            created_at='2025-05-01 10:00:00',
            updated_at='2025-05-01 10:00:00'
        )

        self.alert = HistoricalJudgeAlertInstruction.objects.create(
            id='ALERT001',
            device_id='TEST001',
            ns_latitude_identifier='N',
            latitude=356812,
            ew_longitude_identifier='E',
            longitude=1397671,
            utm_x='539750',
            utm_y='3938100',
            x_acceleration=100,
            y_acceleration=200,
            z_acceleration=300,
            alert_instruction=1,
            alert_id='ALERT001',
            previous_alert_id='PREV001',
            work_time='01:30:00',
            group_num='1111',
            alive_count=1,
            created_at='2025-05-01 10:00:00',
            updated_at='2025-05-01 10:00:00'
        )

        # Set up API client
        self.client = APIClient()

    def test_devices_list(self):
        """Test listing devices"""
        # Login as manager
        self.client.force_authenticate(user=self.manager_user)

        # Make request
        url = reverse('devices-list')
        response = self.client.get(url)

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(len(response.data['data']['devices']), 1)
        self.assertEqual(response.data['data']['devices'][0]['id'], 'TEST001')
        self.assertEqual(response.data['data']['devices'][0]['name'], 'Test Device')

    def test_works_list(self):
        """Test listing works"""
        # Login as manager
        self.client.force_authenticate(user=self.manager_user)

        # Make request
        url = reverse('works-list')
        response = self.client.get(url)

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('WORK001', response.data['data']['works'])

    def test_alerts_list(self):
        """Test listing alerts"""
        # Login as admin
        self.client.force_authenticate(user=self.admin_user)

        # Make request
        url = reverse('alerts-list')
        response = self.client.get(url)

        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(len(response.data['data']['alerts']), 1)
        self.assertEqual(response.data['data']['alerts'][0]['id'], 'ALERT001')
        self.assertEqual(response.data['data']['alerts'][0]['alert_type'], 'approach')
