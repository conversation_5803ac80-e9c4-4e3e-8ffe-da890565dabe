from rest_framework import serializers
from django.contrib.auth import get_user_model
from lips.models import (
    <PERSON>ceMaster,
    DeviceReceiveData,
    HistoricalJudgeAlertInstruction,
    PermitedApproachInfo,
    ProhibitedApproachInfo,
    WorkInfo
)
from datetime import timedelta

User = get_user_model()

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'name', 'email', 'role', 'date_joined', 'last_login']
        read_only_fields = ['id', 'date_joined', 'last_login', 'role']

class DeviceSettingSerializer(serializers.Serializer):
    id = serializers.IntegerField(read_only=True)
    approach_distance = serializers.FloatField(default=5.0)
    approach_seconds = serializers.IntegerField(default=30)
    status = serializers.CharField(default='active')

class DeviceSerializer(serializers.ModelSerializer):
    settings = serializers.SerializerMethodField()
    assigned_work = serializers.SerializerMethodField()
    name = serializers.CharField(source='device_name')
    device_id = serializers.CharField(source='display_device_id')
    charge = serializers.IntegerField(source='battery')
    status = serializers.SerializerMethodField()
    usage_time = serializers.SerializerMethodField()

    class Meta:
        model = DeviceMaster
        fields = ['id', 'name', 'device_id', 'assigned_work', 'charge', 'status', 'usage_time', 'settings']

    def get_settings(self, obj):
        # Default settings if no specific settings found
        return {
            'id': 1,
            'approach_distance': 5.0,
            'approach_seconds': obj.signal_period or 30,
            'status': 'active'
        }

    def get_assigned_work(self, obj):
        return obj.work_id

    def get_status(self, obj):
        # Determine status based on battery level and previous alert
        if obj.battery is None or obj.battery < 20:
            return 'warning'
        elif obj.previous_alert_instruction == '1':
            return 'error'
        else:
            return 'normal'

    def get_usage_time(self, obj):
        # Convert work_time string to duration if possible
        if obj.work_time:
            try:
                parts = obj.work_time.split(':')
                if len(parts) == 3:
                    hours, minutes, seconds = map(int, parts)
                    return timedelta(hours=hours, minutes=minutes, seconds=seconds)
                elif len(parts) == 2:
                    hours, minutes = map(int, parts)
                    return timedelta(hours=hours, minutes=minutes)
            except (ValueError, AttributeError):
                pass
        return None

class DeviceAssignmentSerializer(serializers.Serializer):
    assigned_work = serializers.CharField(allow_null=True, required=False)

    def validate_assigned_work(self, value):
        if value and not WorkInfo.objects.filter(work_name=value).exists():
            raise serializers.ValidationError(f"Work '{value}' does not exist.")
        return value

class WorkGroupSerializer(serializers.Serializer):
    id = serializers.CharField(read_only=True)
    work = serializers.CharField(source='work_name')
    group_number = serializers.CharField(source='group_num')
    time_elapsed = serializers.SerializerMethodField()
    assigned_devices = serializers.SerializerMethodField()
    accessible_areas = serializers.SerializerMethodField()

    def get_time_elapsed(self, obj):
        # Default time elapsed
        return "00:00:00"

    def get_assigned_devices(self, obj):
        return DeviceMaster.objects.filter(work_id=obj.work_name).count()

    def get_accessible_areas(self, obj):
        return PermitedApproachInfo.objects.filter(work_id=obj.work_name).count()

class WorkSerializer(serializers.ModelSerializer):
    work_name = serializers.CharField()

    class Meta:
        model = WorkInfo
        fields = ['id', 'work_name']

class AlertSerializer(serializers.ModelSerializer):
    device = serializers.SerializerMethodField()
    alert_type = serializers.SerializerMethodField()
    message = serializers.SerializerMethodField()
    is_read = serializers.SerializerMethodField()
    created_at = serializers.CharField(source='created_at')

    class Meta:
        model = HistoricalJudgeAlertInstruction
        fields = ['id', 'device', 'alert_type', 'message', 'is_read', 'created_at']

    def get_device(self, obj):
        try:
            device = DeviceMaster.objects.get(id=obj.device_id)
            return DeviceSerializer(device).data
        except DeviceMaster.DoesNotExist:
            return None

    def get_alert_type(self, obj):
        # Determine alert type based on alert_instruction
        if obj.alert_instruction == 1:
            return 'approach'
        elif obj.alert_instruction == 2:
            return 'battery'
        elif obj.alert_instruction == 3:
            return 'error'
        else:
            return 'offline'

    def get_message(self, obj):
        # Generate message based on alert type
        alert_type = self.get_alert_type(obj)
        if alert_type == 'approach':
            return 'Device is approaching a prohibited area'
        elif alert_type == 'battery':
            return 'Device battery is low'
        elif alert_type == 'error':
            return 'Device communication error'
        else:
            return 'Device has gone offline'

    def get_is_read(self, obj):
        # Default to not read
        return False
