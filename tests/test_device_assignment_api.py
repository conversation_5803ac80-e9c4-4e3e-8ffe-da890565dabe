#!/usr/bin/env python
"""
Device Assignment API Test
Tests the device work assignment and removal functionality.
This test verifies:
1. Assigning work to devices
2. Removing work from devices
3. Device status updates
4. Error handling for invalid assignments
"""

import os
import sys
import django
import requests
import json
import time
import subprocess
import signal

# Set up Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

BASE_URL = "http://localhost:8000"

def start_server():
    """Start the Django development server"""
    print("Starting Django development server...")
    server_process = subprocess.Popen(
        ["python", "manage.py", "runserver", "8000"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        preexec_fn=os.setsid
    )
    time.sleep(3)
    return server_process

def stop_server(server_process):
    """Stop the Django development server"""
    print("Stopping Django development server...")
    try:
        os.killpg(os.getpgid(server_process.pid), signal.SIGTERM)
        server_process.wait()
    except ProcessLookupError:
        print("Process already terminated.")

def get_auth_token():
    """Get authentication token"""
    login_data = {
        "email": "<EMAIL>",
        "password": "admin"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/auth/login/", json=login_data)
    if response.status_code == 200:
        return response.json().get('access_token')
    return None

def get_test_device_and_work():
    """Get a test device and work for assignment"""
    token = get_auth_token()
    headers = {'Authorization': f'Bearer {token}'}

    # Get devices
    devices_response = requests.get(f"{BASE_URL}/api/v1/lipsapi/device/", headers=headers)
    if devices_response.status_code != 200:
        return None, None

    devices = devices_response.json()['data']['devices']
    if not devices:
        return None, None

    device_id = devices[0]['id']

    # Get works
    works_response = requests.get(f"{BASE_URL}/api/v1/lipsapi/works/", headers=headers)
    if works_response.status_code != 200:
        return device_id, None

    works = works_response.json()['data']['works']
    if not works:
        return device_id, None

    work_id = works[0]['id']  # Get work ID instead of name

    return device_id, work_id

def test_device_assignment():
    """Test device work assignment"""
    print("\n🔧 Testing Device Assignment API...")
    
    # Get authentication token
    token = get_auth_token()
    if not token:
        print("❌ Failed to get authentication token")
        return False
    
    headers = {'Authorization': f'Bearer {token}'}
    
    # Get test device and work
    device_id, work_id = get_test_device_and_work()
    if not device_id or not work_id:
        print("❌ Failed to get test device or work")
        return False

    print(f"Using device ID: {device_id}, work ID: {work_id}")

    # Test 1: Assign work to device
    print("1. Testing POST /api/v1/lipsapi/device/assign-work/")
    assign_data = {
        "deviceId": str(device_id),
        "assignedWork": work_id
    }

    response = requests.post(f"{BASE_URL}/api/v1/lipsapi/device/assign-work/",
                           json=assign_data, headers=headers)
    if response.status_code == 200:
        print("✅ Assign work to device - SUCCESS")
        result = response.json()
        print(f"   Response: {result['message']}")
    else:
        print(f"❌ Assign work to device - FAILED (Status: {response.status_code})")
        print(f"   Response: {response.text}")
        return False
    
    # Test 2: Verify assignment by getting device details
    print(f"2. Testing GET /api/v1/lipsapi/device/{device_id}/")
    response = requests.get(f"{BASE_URL}/api/v1/lipsapi/device/{device_id}/", headers=headers)
    if response.status_code == 200:
        print("✅ Get device details after assignment - SUCCESS")
        device_data = response.json()['data']
        print(f"   Device assigned work: {device_data.get('assignedWork', 'None')}")
    else:
        print(f"❌ Get device details - FAILED (Status: {response.status_code})")
        return False
    
    # Test 3: Remove work from device
    print("3. Testing POST /api/v1/lipsapi/device/remove-work/")
    remove_data = {
        "deviceId": str(device_id),
        "workId": work_id
    }

    response = requests.post(f"{BASE_URL}/api/v1/lipsapi/device/remove-work/",
                           json=remove_data, headers=headers)
    if response.status_code == 200:
        print("✅ Remove work from device - SUCCESS")
        result = response.json()
        print(f"   Response: {result['message']}")
    else:
        print(f"❌ Remove work from device - FAILED (Status: {response.status_code})")
        print(f"   Response: {response.text}")
        return False
    
    # Test 4: Verify removal by getting device details
    print(f"4. Testing GET /api/v1/lipsapi/device/{device_id}/ (after removal)")
    response = requests.get(f"{BASE_URL}/api/v1/lipsapi/device/{device_id}/", headers=headers)
    if response.status_code == 200:
        print("✅ Get device details after removal - SUCCESS")
        device_data = response.json()['data']
        assigned_work = device_data.get('assignedWork', 'None')
        print(f"   Device assigned work: {assigned_work}")
        if assigned_work == 'None' or assigned_work is None:
            print("✅ Work successfully removed from device")
        else:
            print("⚠️  Work may still be assigned to device")
    else:
        print(f"❌ Get device details after removal - FAILED (Status: {response.status_code})")
        return False
    
    print("\n🎉 All Device Assignment tests passed!")
    return True

def test_device_assignment_validation():
    """Test device assignment validation"""
    print("\n🔍 Testing Device Assignment Validation...")
    
    token = get_auth_token()
    if not token:
        print("❌ Failed to get authentication token")
        return False
    
    headers = {'Authorization': f'Bearer {token}'}
    
    # Test 1: Assign non-existent work
    print("1. Testing assignment with non-existent work")
    assign_data = {
        "assignedWork": "NON_EXISTENT_WORK"
    }
    
    response = requests.put(f"{BASE_URL}/api/v1/lipsapi/device/1/assign/", 
                           json=assign_data, headers=headers)
    if response.status_code in [400, 404]:
        print("✅ Non-existent work validation - SUCCESS (Error as expected)")
        print(f"   Status: {response.status_code}")
    else:
        print(f"❌ Non-existent work validation - FAILED (Expected error, got {response.status_code})")
        return False
    
    # Test 2: Assign to non-existent device
    print("2. Testing assignment to non-existent device")
    assign_data = {
        "assignedWork": "SVLR0001"  # Assuming this work exists
    }
    
    response = requests.put(f"{BASE_URL}/api/v1/lipsapi/device/99999/assign/", 
                           json=assign_data, headers=headers)
    if response.status_code in [400, 404]:
        print("✅ Non-existent device validation - SUCCESS (Error as expected)")
        print(f"   Status: {response.status_code}")
    else:
        print(f"❌ Non-existent device validation - FAILED (Expected error, got {response.status_code})")
        return False
    
    print("\n🎉 All Device Assignment validation tests passed!")
    return True

def test_device_status():
    """Test device status API"""
    print("\n📊 Testing Device Status API...")
    
    token = get_auth_token()
    if not token:
        print("❌ Failed to get authentication token")
        return False
    
    headers = {'Authorization': f'Bearer {token}'}
    
    # Test device status endpoint
    print("1. Testing POST /api/v1/lipsapi/device/status/")
    status_data = {
        "deviceId": "0010",  # Use a known device ID
        "workId": 1  # Use a known work ID
    }
    response = requests.post(f"{BASE_URL}/api/v1/lipsapi/device/status/", json=status_data, headers=headers)
    if response.status_code == 200:
        print("✅ Get device status - SUCCESS")
        status_data = response.json()
        print(f"   Response keys: {list(status_data.keys())}")
    else:
        print(f"❌ Get device status - FAILED (Status: {response.status_code})")
        return False
    
    print("\n🎉 Device Status test passed!")
    return True

def main():
    """Main function to run Device Assignment tests"""
    print("=== Device Assignment API Test Suite ===")
    
    # Start the server
    server_process = start_server()
    
    try:
        # Run the tests
        assignment_success = test_device_assignment()
        validation_success = test_device_assignment_validation()
        status_success = test_device_status()
        
        if assignment_success and validation_success and status_success:
            print("\n✅ All Device Assignment API tests passed successfully!")
            return 0
        else:
            print("\n❌ Some Device Assignment API tests failed.")
            return 1
            
    finally:
        # Stop the server
        stop_server(server_process)

if __name__ == "__main__":
    sys.exit(main())
