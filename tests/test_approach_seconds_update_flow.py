#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to test the flow of updating the approach seconds setting.
This script will:
1. Start the Django development server
2. Make an API call to update the approach seconds
3. Check the response value
4. Check the database value immediately after the API call
5. Make another API call to get the device settings
6. Check if the value in the second API call matches the updated value
"""

import os
import sys
import json
import time
import requests
import subprocess
import signal
import django
from django.utils import timezone
from pprint import pprint

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Import models after Django setup
from lips.models import DeviceMaster, SettingsInfo, UserMaster

def start_server():
    """Start the Django development server"""
    print("Starting Django development server...")
    server_process = subprocess.Popen(
        ["python", "manage.py", "runserver", "8000"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        preexec_fn=os.setsid  # Use this to create a new process group
    )
    
    # Wait for the server to start
    time.sleep(3)
    
    return server_process

def stop_server(server_process):
    """Stop the Django development server"""
    print("Stopping Django development server...")
    try:
        os.killpg(os.getpgid(server_process.pid), signal.SIGTERM)
        server_process.wait()
    except ProcessLookupError:
        print("Process already terminated.")

def get_auth_token():
    """Get an authentication token for API requests"""
    print("Getting authentication token...")
    
    # Create admin user if it doesn't exist
    from django.contrib.auth.hashers import make_password
    from usermanagement.models import User, Role
    
    # Get admin role
    admin_role = Role.objects.filter(name='admin').first()
    if not admin_role:
        print("Creating admin role...")
        admin_role = Role.objects.create(id=1, name='admin')
    
    # Create admin user if it doesn't exist
    admin_user = User.objects.filter(username='admin').first()
    if not admin_user:
        print("Creating admin user...")
        admin_user = User.objects.create(
            username='admin',
            password=make_password('admin'),
            email='<EMAIL>',
            role=admin_role,
            full_name='Admin User',
            status='active'
        )
    
    # Get token using the API
    url = "http://localhost:8000/api/v1/userapi/token/"
    data = {
        "username": "admin",
        "password": "admin"
    }
    
    try:
        response = requests.post(url, json=data)
        if response.status_code == 200:
            token_data = response.json()
            print("Authentication token obtained successfully.")
            return token_data.get('access')
        else:
            print(f"Failed to get authentication token: {response.text}")
            return None
    except Exception as e:
        print(f"Error getting authentication token: {e}")
        return None

def get_device():
    """Get a device from the database"""
    print("Getting a device from the database...")
    device = DeviceMaster.objects.first()
    if not device:
        print("No devices found in the database.")
        return None
    
    print(f"Found device: ID={device.id}, Name={device.device_name}")
    return device

def get_approach_seconds_from_db(user_id):
    """Get the approach seconds setting from the database"""
    print(f"Getting approach seconds from database for user {user_id}...")
    
    # Get estimationSec setting with user ID
    setting_with_user_id = SettingsInfo.objects.filter(
        id=user_id,
        key_name='estimationSec'
    ).first()
    
    # Get estimationSec setting with user ID prefix
    setting_with_prefix = SettingsInfo.objects.filter(
        id=f"{user_id}_estimationSec",
        key_name='estimationSec'
    ).first()
    
    if setting_with_user_id:
        value = int(setting_with_user_id.value)
        print(f"  Setting with user ID: {setting_with_user_id.id}, Value: {value}")
        return value
    
    if setting_with_prefix:
        value = int(setting_with_prefix.value)
        print(f"  Setting with user ID prefix: {setting_with_prefix.id}, Value: {value}")
        return value
    
    print("  No setting found.")
    return None

def update_approach_seconds_api(device_id, new_value, token):
    """Update the approach seconds setting through the API"""
    print(f"Updating approach seconds setting for device ID {device_id} to {new_value} through the API...")
    
    url = f"http://localhost:8000/api/v1/lipsapi/device/settings/{device_id}/"
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    
    # Get the device to get the current values
    device = DeviceMaster.objects.get(id=device_id)
    
    # Prepare the update data
    update_data = {
        'name': device.device_name,
        'deviceId': device.display_device_id,
        'signalPeriod': device.signal_period,
        'approachDistance': 5.0,  # Default value
        'approachSeconds': new_value
    }
    
    try:
        response = requests.put(url, json=update_data, headers=headers)
        print(f"Response status code: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print("Response data:")
            pprint(response_data)
            return response_data
        else:
            print(f"Error: {response.text}")
            return None
    except Exception as e:
        print(f"Error sending request: {e}")
        return None

def get_device_settings_api(device_id, token):
    """Get the device settings through the API"""
    print(f"Getting device settings for device ID {device_id} through the API...")
    
    url = f"http://localhost:8000/api/v1/lipsapi/device/settings/{device_id}/"
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    
    try:
        response = requests.get(url, headers=headers)
        print(f"Response status code: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print("Response data:")
            pprint(response_data)
            return response_data
        else:
            print(f"Error: {response.text}")
            return None
    except Exception as e:
        print(f"Error sending request: {e}")
        return None

def test_update_flow(device_id, user_id, token):
    """Test the flow of updating the approach seconds setting"""
    print(f"Testing update flow for device ID {device_id}...")
    
    # Get the current approach seconds setting
    current_value = get_approach_seconds_from_db(user_id)
    if current_value is None:
        print("No approach seconds setting found.")
        return False
    
    # Calculate a new value
    new_value = current_value + 10
    
    # Update the approach seconds setting through the API
    update_response = update_approach_seconds_api(device_id, new_value, token)
    if not update_response:
        print("Failed to update approach seconds through API.")
        return False
    
    # Get the value from the API response
    response_value = update_response.get('data', {}).get('approachSeconds')
    print(f"Value from API response: {response_value}")
    
    # Check the database value immediately after the API call
    db_value_after_update = get_approach_seconds_from_db(user_id)
    print(f"Value from database after update: {db_value_after_update}")
    
    # Make another API call to get the device settings
    get_response = get_device_settings_api(device_id, token)
    if not get_response:
        print("Failed to get device settings through API.")
        return False
    
    # Get the value from the second API call
    second_response_value = get_response.get('data', {}).get('approachSeconds')
    print(f"Value from second API response: {second_response_value}")
    
    # Check if all values match
    if response_value == new_value and db_value_after_update == new_value and second_response_value == new_value:
        print("All values match the new value!")
        return True
    else:
        print("Values do not match:")
        print(f"  Expected value: {new_value}")
        print(f"  First API response value: {response_value}")
        print(f"  Database value after update: {db_value_after_update}")
        print(f"  Second API response value: {second_response_value}")
        return False

def main():
    """Main function"""
    print("=== Approach Seconds Update Flow Test ===")
    
    # Start the server
    server_process = start_server()
    
    try:
        # Get authentication token
        token = get_auth_token()
        if not token:
            print("Failed to get authentication token.")
            return 1
        
        # Get a device from the database
        device = get_device()
        if not device:
            print("No device to test with.")
            return 1
        
        # Get the user ID
        user_id = device.user_id.id if device.user_id else None
        if not user_id:
            print("Device has no user ID.")
            return 1
        
        # Test the update flow
        success = test_update_flow(device.id, user_id, token)
        
        if success:
            print("\nTest completed successfully!")
            return 0
        else:
            print("\nTest failed.")
            return 1
    
    finally:
        # Stop the server
        stop_server(server_process)

if __name__ == "__main__":
    sys.exit(main())
