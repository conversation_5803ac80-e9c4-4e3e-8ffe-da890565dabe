#!/usr/bin/env python
"""
Alert API CRUD Test
Tests the newly created Alert API endpoints for full CRUD operations.
This test verifies:
1. Creating alerts
2. Reading alerts (list and detail)
3. Updating alerts
4. Deleting alerts
5. Mark as read functionality
"""

import os
import sys
import django
import requests
import json
import time
import subprocess
import signal

# Set up Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

BASE_URL = "http://localhost:8000"

def start_server():
    """Start the Django development server"""
    print("Starting Django development server...")
    server_process = subprocess.Popen(
        ["python", "manage.py", "runserver", "8000"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        preexec_fn=os.setsid
    )
    time.sleep(3)
    return server_process

def stop_server(server_process):
    """Stop the Django development server"""
    print("Stopping Django development server...")
    try:
        os.killpg(os.getpgid(server_process.pid), signal.SIGTERM)
        server_process.wait()
    except ProcessLookupError:
        print("Process already terminated.")

def get_auth_token():
    """Get authentication token"""
    login_data = {
        "email": "<EMAIL>",
        "password": "admin"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/auth/login/", json=login_data)
    if response.status_code == 200:
        return response.json().get('access_token')
    return None

def test_alert_crud():
    """Test Alert CRUD operations"""
    print("\n🚨 Testing Alert API CRUD Operations...")
    
    # Get authentication token
    token = get_auth_token()
    if not token:
        print("❌ Failed to get authentication token")
        return False
    
    headers = {'Authorization': f'Bearer {token}'}
    
    # Test 1: Get all alerts (should be empty initially)
    print("1. Testing GET /api/v1/lipsapi/alerts/")
    response = requests.get(f"{BASE_URL}/api/v1/lipsapi/alerts/", headers=headers)
    if response.status_code == 200:
        print("✅ Get all alerts - SUCCESS")
        initial_alerts = response.json()['data']['alerts']
        print(f"   Initial alerts count: {len(initial_alerts)}")
    else:
        print(f"❌ Get all alerts - FAILED (Status: {response.status_code})")
        return False
    
    # Test 2: Create a new alert
    print("2. Testing POST /api/v1/lipsapi/alerts/")
    alert_data = {
        "device": 1,  # Assuming device with ID 1 exists
        "alert_type": "approach",
        "message": "Test alert message",
        "is_read": False
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/lipsapi/alerts/", json=alert_data, headers=headers)
    if response.status_code in [200, 201]:
        print("✅ Create alert - SUCCESS")
        created_alert = response.json()['data']
        alert_id = created_alert['id']
        print(f"   Created alert ID: {alert_id}")
        print(f"   Alert type: {created_alert['alert_type']}")
        print(f"   Message: {created_alert['message']}")
    else:
        print(f"❌ Create alert - FAILED (Status: {response.status_code})")
        print(f"   Response: {response.text}")
        return False
    
    # Test 3: Get alert by ID
    print(f"3. Testing GET /api/v1/lipsapi/alerts/{alert_id}/")
    response = requests.get(f"{BASE_URL}/api/v1/lipsapi/alerts/{alert_id}/", headers=headers)
    if response.status_code == 200:
        print("✅ Get alert by ID - SUCCESS")
        alert_detail = response.json()['data']
        print(f"   Retrieved alert: {alert_detail['message']}")
    else:
        print(f"❌ Get alert by ID - FAILED (Status: {response.status_code})")
        return False
    
    # Test 4: Update alert
    print(f"4. Testing PUT /api/v1/lipsapi/alerts/{alert_id}/")
    update_data = {
        "alert_type": "entry",
        "message": "Updated test alert message"
    }
    
    response = requests.put(f"{BASE_URL}/api/v1/lipsapi/alerts/{alert_id}/", json=update_data, headers=headers)
    if response.status_code == 200:
        print("✅ Update alert - SUCCESS")
        updated_alert = response.json()['data']
        print(f"   Updated alert type: {updated_alert['alert_type']}")
        print(f"   Updated message: {updated_alert['message']}")
    else:
        print(f"❌ Update alert - FAILED (Status: {response.status_code})")
        print(f"   Response: {response.text}")
        return False
    
    # Test 5: Mark alert as read
    print(f"5. Testing POST /api/v1/lipsapi/alerts/{alert_id}/mark-read/")
    response = requests.post(f"{BASE_URL}/api/v1/lipsapi/alerts/{alert_id}/mark-read/", headers=headers)
    if response.status_code == 200:
        print("✅ Mark alert as read - SUCCESS")
        print(f"   Response: {response.json()['message']}")
    else:
        print(f"❌ Mark alert as read - FAILED (Status: {response.status_code})")
        return False
    
    # Test 6: Delete alert
    print(f"6. Testing DELETE /api/v1/lipsapi/alerts/{alert_id}/")
    response = requests.delete(f"{BASE_URL}/api/v1/lipsapi/alerts/{alert_id}/", headers=headers)
    if response.status_code == 200:
        print("✅ Delete alert - SUCCESS")
        print(f"   Response: {response.json()['message']}")
    else:
        print(f"❌ Delete alert - FAILED (Status: {response.status_code})")
        return False
    
    # Test 7: Verify alert is deleted
    print(f"7. Testing GET /api/v1/lipsapi/alerts/{alert_id}/ (should fail)")
    response = requests.get(f"{BASE_URL}/api/v1/lipsapi/alerts/{alert_id}/", headers=headers)
    if response.status_code == 404:
        print("✅ Verify alert deletion - SUCCESS (404 as expected)")
    else:
        print(f"❌ Verify alert deletion - FAILED (Expected 404, got {response.status_code})")
        return False
    
    print("\n🎉 All Alert API CRUD tests passed!")
    return True

def test_alert_validation():
    """Test Alert API validation"""
    print("\n🔍 Testing Alert API Validation...")
    
    token = get_auth_token()
    if not token:
        print("❌ Failed to get authentication token")
        return False
    
    headers = {'Authorization': f'Bearer {token}'}
    
    # Test invalid device ID
    print("1. Testing invalid device ID")
    invalid_alert_data = {
        "device": 99999,  # Non-existent device
        "alert_type": "approach",
        "message": "Test alert message",
        "is_read": False
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/lipsapi/alerts/", json=invalid_alert_data, headers=headers)
    if response.status_code == 404:
        print("✅ Invalid device ID validation - SUCCESS (404 as expected)")
    else:
        print(f"❌ Invalid device ID validation - FAILED (Expected 404, got {response.status_code})")
        return False
    
    # Test invalid alert type
    print("2. Testing invalid alert type")
    invalid_alert_data = {
        "device": 1,
        "alert_type": "invalid_type",
        "message": "Test alert message",
        "is_read": False
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/lipsapi/alerts/", json=invalid_alert_data, headers=headers)
    if response.status_code == 400:
        print("✅ Invalid alert type validation - SUCCESS (400 as expected)")
    else:
        print(f"❌ Invalid alert type validation - FAILED (Expected 400, got {response.status_code})")
        return False
    
    print("\n🎉 All Alert API validation tests passed!")
    return True

def main():
    """Main function to run Alert API tests"""
    print("=== Alert API CRUD Test Suite ===")
    
    # Start the server
    server_process = start_server()
    
    try:
        # Run the tests
        crud_success = test_alert_crud()
        validation_success = test_alert_validation()
        
        if crud_success and validation_success:
            print("\n✅ All Alert API tests passed successfully!")
            return 0
        else:
            print("\n❌ Some Alert API tests failed.")
            return 1
            
    finally:
        # Stop the server
        stop_server(server_process)

if __name__ == "__main__":
    sys.exit(main())
