# Test Scripts

This directory contains various test scripts for the LIPS application.

## Available Tests

- **test_all_fields_update.py**: Tests that all fields in the DeviceMaster model can be updated properly.
- **test_approach_seconds_api.py**: Tests updating the approach seconds setting through the API.
- **test_approach_seconds_discrepancy.py**: Tests for discrepancies between the API response and database values.
- **test_approach_seconds_update_flow.py**: Tests the complete flow of updating approach seconds.
- **test_device_api_update.py**: Tests updating device settings through the API.
- **test_device_master_update.py**: Tests updating the DeviceMaster model directly.
- **test_user_auth_update.py**: Tests that the user ID is correctly obtained from the authenticated user.

## Running Tests

To run a test, activate the virtual environment and execute the script:

```bash
source venv/bin/activate
python tests/test_script_name.py
```

## Notes

- These tests are standalone scripts and not part of a formal test framework.
- Each test creates its own test data and cleans up after itself.
- Some tests require the Django development server to be running, which they will start and stop automatically.
