#!/usr/bin/env python
"""
Comprehensive API Test Suite
Tests all APIs defined in the Postman collection JSON file
This script will:
1. Start the Django development server
2. Test all authentication, user, device, work, and alert APIs
3. Verify CRUD operations work correctly
4. Clean up test data
"""

import os
import sys
import django
import requests
import json
import time
import subprocess
import signal
from datetime import datetime

# Set up Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Base URL for the API
BASE_URL = "http://localhost:8000"

def start_server():
    """Start the Django development server"""
    print("Starting Django development server...")
    server_process = subprocess.Popen(
        ["python", "manage.py", "runserver", "8000"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        preexec_fn=os.setsid  # Use this to create a new process group
    )

    # Wait for the server to start
    time.sleep(3)

    return server_process

def stop_server(server_process):
    """Stop the Django development server"""
    print("Stopping Django development server...")
    try:
        os.killpg(os.getpgid(server_process.pid), signal.SIGTERM)
        server_process.wait()
    except ProcessLookupError:
        print("Process already terminated.")

class APITester:
    def __init__(self):
        self.base_url = BASE_URL
        self.token = None
        self.test_results = []
        self.created_resources = {
            'users': [],
            'devices': [],
            'works': [],
            'alerts': []
        }

    def log_test(self, test_name, success, message="", response_data=None):
        """Log test results"""
        result = {
            'test': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now().isoformat(),
            'response_data': response_data
        }
        self.test_results.append(result)
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")

    def make_request(self, method, endpoint, data=None, headers=None):
        """Make HTTP request with error handling"""
        url = f"{self.base_url}{endpoint}"
        
        # Add authorization header if token exists
        if self.token and headers is None:
            headers = {}
        if self.token:
            headers = headers or {}
            headers['Authorization'] = f'Bearer {self.token}'
        
        try:
            if method.upper() == 'GET':
                response = requests.get(url, headers=headers)
            elif method.upper() == 'POST':
                response = requests.post(url, json=data, headers=headers)
            elif method.upper() == 'PUT':
                response = requests.put(url, json=data, headers=headers)
            elif method.upper() == 'DELETE':
                response = requests.delete(url, headers=headers)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            return response
        except requests.exceptions.RequestException as e:
            return None

    def test_authentication_apis(self):
        """Test all authentication endpoints"""
        print("\n🔐 Testing Authentication APIs...")
        
        # Test 1: Register User (Skip - using existing admin user)
        self.log_test("Register User", True, "Skipped - using existing admin user")

        # Test 2: Login User (use existing admin user)
        login_data = {
            "email": "<EMAIL>",
            "password": "admin"
        }
        
        response = self.make_request('POST', '/api/v1/auth/login/', login_data)
        if response and response.status_code == 200:
            try:
                response_data = response.json()
                if 'access_token' in response_data:
                    self.token = response_data['access_token']
                    self.log_test("Login User", True, "Login successful, token obtained")
                else:
                    self.log_test("Login User", False, "Login response missing access token")
            except json.JSONDecodeError:
                self.log_test("Login User", False, "Invalid JSON response")
        else:
            self.log_test("Login User", False, f"Failed with status {response.status_code if response else 'No response'}")

        # Test 3: Forgot Password
        forgot_data = {"email": "<EMAIL>"}
        response = self.make_request('POST', '/api/v1/auth/forgot-password/', forgot_data)
        if response and response.status_code in [200, 201]:
            self.log_test("Forgot Password", True, "Forgot password request sent")
        else:
            self.log_test("Forgot Password", False, f"Failed with status {response.status_code if response else 'No response'}")

    def test_user_apis(self):
        """Test user management endpoints"""
        print("\n👤 Testing User APIs...")
        
        # Test 1: Get Current User
        response = self.make_request('GET', '/api/v1/users/me/')
        if response and response.status_code == 200:
            self.log_test("Get Current User", True, "User profile retrieved")
        else:
            self.log_test("Get Current User", False, f"Failed with status {response.status_code if response else 'No response'}")

        # Test 2: Update User Profile
        update_data = {
            "full_name": "Updated Admin User",
            "email": "<EMAIL>"
        }
        response = self.make_request('PUT', '/api/v1/users/me/', update_data)
        if response and response.status_code == 200:
            self.log_test("Update User Profile", True, "User profile updated")
        else:
            self.log_test("Update User Profile", False, f"Failed with status {response.status_code if response else 'No response'}")

    def test_device_apis(self):
        """Test device management endpoints"""
        print("\n📱 Testing Device APIs...")
        
        # Test 1: Get All Devices
        response = self.make_request('GET', '/api/v1/lipsapi/device/')
        if response and response.status_code == 200:
            self.log_test("Get All Devices", True, "Devices list retrieved")
        else:
            self.log_test("Get All Devices", False, f"Failed with status {response.status_code if response else 'No response'}")

        # Test 2: Create Device
        device_data = {
            "name": "Test Device",
            "deviceId": "TEST001",
            "charge": 100,
            "status": "active"
        }
        response = self.make_request('POST', '/api/v1/lipsapi/device/', device_data)
        if response and response.status_code in [200, 201]:
            try:
                response_data = response.json()
                if 'data' in response_data and 'id' in response_data['data']:
                    device_id = response_data['data']['id']
                    self.created_resources['devices'].append(device_id)
                    self.log_test("Create Device", True, f"Device created with ID {device_id}")
                else:
                    self.log_test("Create Device", False, "Device created but no ID returned")
            except json.JSONDecodeError:
                self.log_test("Create Device", False, "Invalid JSON response")
        else:
            self.log_test("Create Device", False, f"Failed with status {response.status_code if response else 'No response'}")

        # Test 3: Get Device by ID (if we have a device ID)
        if self.created_resources['devices']:
            device_id = self.created_resources['devices'][0]
            response = self.make_request('GET', f'/api/v1/lipsapi/device/{device_id}/')
            if response and response.status_code == 200:
                self.log_test("Get Device by ID", True, f"Device {device_id} retrieved")
            else:
                self.log_test("Get Device by ID", False, f"Failed with status {response.status_code if response else 'No response'}")

    def test_device_settings_apis(self):
        """Test device settings endpoints"""
        print("\n⚙️ Testing Device Settings APIs...")
        
        # Test 1: Get All Device Settings
        response = self.make_request('GET', '/api/v1/lipsapi/device/settings/')
        if response and response.status_code == 200:
            self.log_test("Get All Device Settings", True, "Device settings retrieved")
        else:
            self.log_test("Get All Device Settings", False, f"Failed with status {response.status_code if response else 'No response'}")

    def test_works_apis(self):
        """Test work management endpoints"""
        print("\n🔧 Testing Works APIs...")
        
        # Test 1: Get All Works
        response = self.make_request('GET', '/api/v1/lipsapi/works/')
        if response and response.status_code == 200:
            self.log_test("Get All Works", True, "Works list retrieved")
        else:
            self.log_test("Get All Works", False, f"Failed with status {response.status_code if response else 'No response'}")

        # Test 2: Create Work
        work_data = {
            "work_name": "TEST_WORK_001",
            "group_num": "GROUP_001"
        }
        response = self.make_request('POST', '/api/v1/lipsapi/works/', work_data)
        if response and response.status_code in [200, 201]:
            try:
                response_data = response.json()
                if 'data' in response_data and 'id' in response_data['data']:
                    work_id = response_data['data']['id']
                    self.created_resources['works'].append(work_id)
                    self.log_test("Create Work", True, f"Work created with ID {work_id}")
                else:
                    self.log_test("Create Work", False, "Work created but no ID returned")
            except json.JSONDecodeError:
                self.log_test("Create Work", False, "Invalid JSON response")
        else:
            self.log_test("Create Work", False, f"Failed with status {response.status_code if response else 'No response'}")

    def test_alert_apis(self):
        """Test alert management endpoints"""
        print("\n🚨 Testing Alert APIs...")

        # Test 1: Get All Alerts
        response = self.make_request('GET', '/api/v1/lipsapi/alerts/')
        if response and response.status_code == 200:
            self.log_test("Get All Alerts", True, "Alerts list retrieved")
        else:
            self.log_test("Get All Alerts", False, f"Failed with status {response.status_code if response else 'No response'}")

        # Test 2: Create Alert (if we have a device)
        if self.created_resources['devices']:
            device_id = self.created_resources['devices'][0]
            alert_data = {
                "device": int(device_id),
                "alert_type": "approach",
                "message": "Test alert message",
                "is_read": False
            }
            response = self.make_request('POST', '/api/v1/lipsapi/alerts/', alert_data)
            if response and response.status_code in [200, 201]:
                try:
                    response_data = response.json()
                    if 'data' in response_data and 'id' in response_data['data']:
                        alert_id = response_data['data']['id']
                        self.created_resources['alerts'].append(alert_id)
                        self.log_test("Create Alert", True, f"Alert created with ID {alert_id}")
                    else:
                        self.log_test("Create Alert", False, "Alert created but no ID returned")
                except json.JSONDecodeError:
                    self.log_test("Create Alert", False, "Invalid JSON response")
            else:
                self.log_test("Create Alert", False, f"Failed with status {response.status_code if response else 'No response'}")

        # Test 3: Get Alert by ID (if we have an alert)
        if self.created_resources['alerts']:
            alert_id = self.created_resources['alerts'][0]
            response = self.make_request('GET', f'/api/v1/lipsapi/alerts/{alert_id}/')
            if response and response.status_code == 200:
                self.log_test("Get Alert by ID", True, f"Alert {alert_id} retrieved")
            else:
                self.log_test("Get Alert by ID", False, f"Failed with status {response.status_code if response else 'No response'}")

        # Test 4: Update Alert (if we have an alert)
        if self.created_resources['alerts']:
            alert_id = self.created_resources['alerts'][0]
            update_data = {
                "alert_type": "entry",
                "message": "Updated alert message"
            }
            response = self.make_request('PUT', f'/api/v1/lipsapi/alerts/{alert_id}/', update_data)
            if response and response.status_code == 200:
                self.log_test("Update Alert", True, f"Alert {alert_id} updated")
            else:
                self.log_test("Update Alert", False, f"Failed with status {response.status_code if response else 'No response'}")

        # Test 5: Mark Alert as Read (if we have an alert)
        if self.created_resources['alerts']:
            alert_id = self.created_resources['alerts'][0]
            response = self.make_request('POST', f'/api/v1/lipsapi/alerts/{alert_id}/mark-read/')
            if response and response.status_code == 200:
                self.log_test("Mark Alert as Read", True, f"Alert {alert_id} marked as read")
            else:
                self.log_test("Mark Alert as Read", False, f"Failed with status {response.status_code if response else 'No response'}")

    def test_additional_device_apis(self):
        """Test additional device endpoints"""
        print("\n📱 Testing Additional Device APIs...")

        # Test Update Device (if we have a device)
        if self.created_resources['devices']:
            device_id = self.created_resources['devices'][0]
            update_data = {
                "name": "Updated Test Device",
                "charge": 85,
                "status": "normal"
            }
            response = self.make_request('PUT', f'/api/v1/lipsapi/device/{device_id}/', update_data)
            if response and response.status_code == 200:
                self.log_test("Update Device", True, f"Device {device_id} updated")
            else:
                self.log_test("Update Device", False, f"Failed with status {response.status_code if response else 'No response'}")

        # Test Device Assignment (if we have device and work)
        if self.created_resources['devices'] and self.created_resources['works']:
            device_id = self.created_resources['devices'][0]
            work_name = "TEST_WORK_001"  # Use the work we created
            assign_data = {
                "assignedWork": work_name
            }
            response = self.make_request('PUT', f'/api/v1/lipsapi/device/{device_id}/assign/', assign_data)
            if response and response.status_code == 200:
                self.log_test("Device Assignment", True, f"Work assigned to device {device_id}")
            else:
                self.log_test("Device Assignment", False, f"Failed with status {response.status_code if response else 'No response'}")

    def test_additional_works_apis(self):
        """Test additional work endpoints"""
        print("\n🔧 Testing Additional Works APIs...")

        # Test Get Work Details (if we have a work)
        if self.created_resources['works']:
            work_id = self.created_resources['works'][0]
            response = self.make_request('GET', f'/api/v1/lipsapi/works/{work_id}/')
            if response and response.status_code == 200:
                self.log_test("Get Work Details", True, f"Work {work_id} details retrieved")
            else:
                self.log_test("Get Work Details", False, f"Failed with status {response.status_code if response else 'No response'}")

        # Test Update Work (if we have a work)
        if self.created_resources['works']:
            work_id = self.created_resources['works'][0]
            update_data = {
                "work_name": "UPDATED_TEST_WORK_001"
            }
            response = self.make_request('PUT', f'/api/v1/lipsapi/works/{work_id}/', update_data)
            if response and response.status_code == 200:
                self.log_test("Update Work", True, f"Work {work_id} updated")
            else:
                self.log_test("Update Work", False, f"Failed with status {response.status_code if response else 'No response'}")

    def cleanup_resources(self):
        """Clean up created test resources"""
        print("\n🧹 Cleaning up test resources...")

        # Delete alerts
        for alert_id in self.created_resources['alerts']:
            response = self.make_request('DELETE', f'/api/v1/lipsapi/alerts/{alert_id}/')
            if response and response.status_code in [200, 204]:
                self.log_test("Delete Alert", True, f"Alert {alert_id} deleted")
            else:
                self.log_test("Delete Alert", False, f"Failed to delete alert {alert_id}")

        # Delete devices
        for device_id in self.created_resources['devices']:
            response = self.make_request('DELETE', f'/api/v1/lipsapi/device/{device_id}/')
            if response and response.status_code in [200, 204]:
                self.log_test("Delete Device", True, f"Device {device_id} deleted")
            else:
                self.log_test("Delete Device", False, f"Failed to delete device {device_id}")

        # Delete works
        for work_id in self.created_resources['works']:
            response = self.make_request('DELETE', f'/api/v1/lipsapi/works/{work_id}/')
            if response and response.status_code in [200, 204]:
                self.log_test("Delete Work", True, f"Work {work_id} deleted")
            else:
                self.log_test("Delete Work", False, f"Failed to delete work {work_id}")

    def run_all_tests(self):
        """Run all API tests"""
        print("🚀 Starting Comprehensive API Test Suite...")
        print(f"Base URL: {self.base_url}")

        # Run tests in order
        self.test_authentication_apis()
        self.test_user_apis()
        self.test_device_apis()
        self.test_device_settings_apis()
        self.test_works_apis()
        self.test_alert_apis()
        self.test_additional_device_apis()
        self.test_additional_works_apis()

        # Clean up resources
        self.cleanup_resources()

        # Print summary
        self.print_summary()

    def print_summary(self):
        """Print test summary"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"\n📊 Test Summary:")
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ Failed Tests:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  - {result['test']}: {result['message']}")

def main():
    """Main function to run tests"""
    print("=== Comprehensive API Test Suite ===")

    # Start the server
    server_process = start_server()

    try:
        # Run the tests
        tester = APITester()
        tester.run_all_tests()

        # Check results
        total_tests = len(tester.test_results)
        passed_tests = sum(1 for result in tester.test_results if result['success'])

        if passed_tests == total_tests:
            print("\nAll tests passed successfully!")
            return 0
        else:
            print(f"\n{total_tests - passed_tests} tests failed.")
            return 1

    finally:
        # Stop the server
        stop_server(server_process)

if __name__ == "__main__":
    sys.exit(main())
