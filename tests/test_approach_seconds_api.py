#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to test updating the approach seconds setting through the API.
This script will:
1. Start the Django development server
2. Get a device from the database
3. Update the approach seconds setting through the API
4. Verify that the setting was updated correctly
"""

import os
import sys
import json
import time
import requests
import subprocess
import signal
import django
from django.utils import timezone
from pprint import pprint

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Import models after Django setup
from lips.models import DeviceMaster, SettingsInfo, UserMaster

def start_server():
    """Start the Django development server"""
    print("Starting Django development server...")
    server_process = subprocess.Popen(
        ["python", "manage.py", "runserver", "8000"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        preexec_fn=os.setsid  # Use this to create a new process group
    )
    
    # Wait for the server to start
    time.sleep(3)
    
    return server_process

def stop_server(server_process):
    """Stop the Django development server"""
    print("Stopping Django development server...")
    os.killpg(os.getpgid(server_process.pid), signal.SIGTERM)
    server_process.wait()

def get_auth_token():
    """Get an authentication token for API requests"""
    print("Getting authentication token...")
    
    # Create admin user if it doesn't exist
    from django.contrib.auth.hashers import make_password
    from usermanagement.models import User, Role
    
    # Get admin role
    admin_role = Role.objects.filter(name='admin').first()
    if not admin_role:
        print("Creating admin role...")
        admin_role = Role.objects.create(id=1, name='admin')
    
    # Create admin user if it doesn't exist
    admin_user = User.objects.filter(username='admin').first()
    if not admin_user:
        print("Creating admin user...")
        admin_user = User.objects.create(
            username='admin',
            password=make_password('admin'),
            email='<EMAIL>',
            role=admin_role,
            full_name='Admin User',
            status='active'
        )
    
    # Get token using the API
    url = "http://localhost:8000/api/v1/userapi/token/"
    data = {
        "username": "admin",
        "password": "admin"
    }
    
    try:
        response = requests.post(url, json=data)
        if response.status_code == 200:
            token_data = response.json()
            print("Authentication token obtained successfully.")
            return token_data.get('access')
        else:
            print(f"Failed to get authentication token: {response.text}")
            return None
    except Exception as e:
        print(f"Error getting authentication token: {e}")
        return None

def get_device():
    """Get a device from the database"""
    print("Getting a device from the database...")
    device = DeviceMaster.objects.first()
    if not device:
        print("No devices found in the database.")
        return None
    
    print(f"Found device: ID={device.id}, Name={device.device_name}")
    return device

def get_approach_seconds(user_id):
    """Get the approach seconds setting for a user"""
    print(f"Getting approach seconds setting for user {user_id}...")
    
    # Get estimationSec setting with user ID
    setting_with_user_id = SettingsInfo.objects.filter(
        id=user_id,
        key_name='estimationSec'
    ).first()
    
    # Get estimationSec setting with user ID prefix
    setting_with_prefix = SettingsInfo.objects.filter(
        id=f"{user_id}_estimationSec",
        key_name='estimationSec'
    ).first()
    
    if setting_with_user_id:
        print(f"  Setting with user ID: {setting_with_user_id.id}, Value: {setting_with_user_id.value}")
        return int(setting_with_user_id.value)
    
    if setting_with_prefix:
        print(f"  Setting with user ID prefix: {setting_with_prefix.id}, Value: {setting_with_prefix.value}")
        return int(setting_with_prefix.value)
    
    print("  No setting found.")
    return None

def update_approach_seconds_api(device_id, new_value, token):
    """Update the approach seconds setting through the API"""
    print(f"Updating approach seconds setting for device ID {device_id} to {new_value} through the API...")
    
    url = f"http://localhost:8000/api/v1/lipsapi/device/settings/{device_id}/"
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    
    # Get the device to get the current values
    device = DeviceMaster.objects.get(id=device_id)
    
    # Prepare the update data
    update_data = {
        'name': device.device_name,
        'deviceId': device.display_device_id,
        'signalPeriod': device.signal_period,
        'approachDistance': 5.0,  # Default value
        'approachSeconds': new_value
    }
    
    try:
        response = requests.put(url, json=update_data, headers=headers)
        print(f"Response status code: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print("Response data:")
            pprint(response_data)
            return response_data
        else:
            print(f"Error: {response.text}")
            return None
    except Exception as e:
        print(f"Error sending request: {e}")
        return None

def verify_approach_seconds_update(user_id, expected_value):
    """Verify that the approach seconds setting was updated correctly"""
    print(f"Verifying approach seconds update for user {user_id}...")
    
    # Get the current value
    current_value = get_approach_seconds(user_id)
    
    if current_value == expected_value:
        print(f"  Success! Current value ({current_value}) matches expected value ({expected_value}).")
        return True
    else:
        print(f"  Failure! Current value ({current_value}) does not match expected value ({expected_value}).")
        return False

def main():
    """Main function"""
    print("=== Approach Seconds API Update Test ===")
    
    # Start the server
    server_process = start_server()
    
    try:
        # Get authentication token
        token = get_auth_token()
        if not token:
            print("Failed to get authentication token.")
            return 1
        
        # Get a device from the database
        device = get_device()
        if not device:
            print("No device to test with.")
            return 1
        
        # Get the user ID
        user_id = device.user_id.id if device.user_id else None
        if not user_id:
            print("Device has no user ID.")
            return 1
        
        # Get the current approach seconds setting
        current_value = get_approach_seconds(user_id)
        if current_value is None:
            print("No approach seconds setting found.")
            return 1
        
        # Calculate a new value
        new_value = current_value + 10
        
        # Update the approach seconds setting through the API
        response_data = update_approach_seconds_api(device.id, new_value, token)
        if not response_data:
            print("Failed to update approach seconds through API.")
            return 1
        
        # Verify that the setting was updated correctly
        success = verify_approach_seconds_update(user_id, new_value)
        
        # Restore the original value
        restore_response = update_approach_seconds_api(device.id, current_value, token)
        if not restore_response:
            print("Failed to restore original value.")
            return 1
        
        # Verify that the setting was restored correctly
        restore_success = verify_approach_seconds_update(user_id, current_value)
        
        if success and restore_success:
            print("\nTest completed successfully!")
            return 0
        else:
            print("\nTest failed.")
            return 1
    
    finally:
        # Stop the server
        stop_server(server_process)

if __name__ == "__main__":
    sys.exit(main())
