#!/usr/bin/env python
"""
<PERSON>rip<PERSON> to test the new approach_area_distance and approach_area_seconds fields.
This script will:
1. Start the Django development server
2. Create a test user in the auth system
3. Create a corresponding UserMaster record
4. Get a device from the database
5. Update the device with approach_area_distance and approach_area_seconds values
6. Verify that the fields are updated correctly
"""

import os
import sys
import json
import time
import requests
import subprocess
import signal
import django
from django.utils import timezone
from pprint import pprint

# Set up Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Import models after Django setup
from lips.models import DeviceMaster, WorkInfo, SettingsInfo, UserMaster
from django.contrib.auth.hashers import make_password
from usermanagement.models import User, Role

def start_server():
    """Start the Django development server"""
    print("Starting Django development server...")
    server_process = subprocess.Popen(
        ["python", "manage.py", "runserver", "8000"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        preexec_fn=os.setsid  # Use this to create a new process group
    )

    # Wait for the server to start
    time.sleep(3)

    return server_process

def stop_server(server_process):
    """Stop the Django development server"""
    print("Stopping Django development server...")
    try:
        os.killpg(os.getpgid(server_process.pid), signal.SIGTERM)
        server_process.wait()
    except ProcessLookupError:
        print("Process already terminated.")

def create_test_user():
    """Create a test user in the auth system and a corresponding UserMaster record"""
    print("Creating test user...")

    # Create admin role if it doesn't exist
    admin_role = Role.objects.filter(name='admin').first()
    if not admin_role:
        print("Creating admin role...")
        admin_role = Role.objects.create(id=1, name='admin')

    # Create test user if it doesn't exist
    test_username = 'test_admin'
    test_user = User.objects.filter(username=test_username).first()
    if not test_user:
        print("Creating test user in auth system...")
        test_user = User.objects.create(
            username=test_username,
            password=make_password('test_password'),
            email='<EMAIL>',
            role=admin_role,
            full_name='Test Admin',
            status='active'
        )
    elif test_user.role != admin_role:
        print(f"Updating role for {test_user.username} to admin")
        test_user.role = admin_role
        test_user.save()

    # Create UserMaster record if it doesn't exist
    user_master = UserMaster.objects.filter(id=test_username).first()
    if not user_master:
        print("Creating UserMaster record...")
        user_master = UserMaster.objects.create(
            id=test_username,
            created_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
            updated_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S')
        )

    print(f"Test user created: {test_username}")
    return test_username

def get_auth_token(username, password):
    """Get an authentication token for API requests"""
    print(f"Getting authentication token for user {username}...")

    url = "http://localhost:8000/api/v1/userapi/token/"
    data = {
        "username": username,
        "password": password
    }

    try:
        response = requests.post(url, json=data)
        if response.status_code == 200:
            token_data = response.json()
            print("Authentication token obtained successfully.")
            return token_data.get('access')
        else:
            print(f"Failed to get authentication token: {response.text}")
            return None
    except Exception as e:
        print(f"Error getting authentication token: {e}")
        return None

def get_device():
    """Get a device from the database"""
    print("Getting a device from the database...")
    device = DeviceMaster.objects.first()
    if not device:
        print("No devices found in the database.")
        return None

    print(f"Found device: ID={device.id}, Name={device.device_name}")
    return device

def print_device_details(device):
    """Print all fields of a device"""
    print("\nDevice details:")
    print(f"ID: {device.id}")
    print(f"User ID: {device.user_id.id if device.user_id else None}")
    print(f"Display Device ID: {device.display_device_id}")
    print(f"Device Name: {device.device_name}")
    print(f"Work ID: {device.work_id.id if device.work_id else None}")
    print(f"Work Time: {device.work_time}")
    print(f"Battery: {device.battery}")
    print(f"Previous Alert Instruction: {device.previous_alert_instruction}")
    print(f"Signal Period: {device.signal_period}")
    print(f"Approach Area Distance: {device.approach_area_distance}")
    print(f"Approach Area Seconds: {device.approach_area_seconds}")
    print(f"Status: {device.status}")
    print(f"Created At: {device.created_at}")
    print(f"Updated At: {device.updated_at}")

def update_device_settings_api(device_id, update_data, token):
    """Update device settings through the API"""
    print(f"Updating device settings for device ID {device_id} through the API...")

    url = f"http://localhost:8000/api/v1/lipsapi/device/settings/{device_id}/"
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }

    try:
        response = requests.put(url, json=update_data, headers=headers)
        print(f"Response status code: {response.status_code}")

        if response.status_code == 200:
            response_data = response.json()
            print("Response data:")
            pprint(response_data)
            return response_data
        else:
            print(f"Error: {response.text}")
            return None
    except Exception as e:
        print(f"Error sending request: {e}")
        return None

def verify_approach_area_fields(device_id, expected_distance, expected_seconds):
    """Verify that the device's approach_area_distance and approach_area_seconds fields are set correctly"""
    print(f"Verifying approach area fields for device ID {device_id}...")

    # Get the device from the database
    device = DeviceMaster.objects.get(id=device_id)

    # Check if the fields are set correctly
    distance_correct = device.approach_area_distance == expected_distance
    seconds_correct = device.approach_area_seconds == expected_seconds

    if distance_correct and seconds_correct:
        print(f"Success! Approach area fields are set correctly:")
        print(f"  approach_area_distance: {device.approach_area_distance} (expected: {expected_distance})")
        print(f"  approach_area_seconds: {device.approach_area_seconds} (expected: {expected_seconds})")
        return True
    else:
        print(f"Failure! Approach area fields are not set correctly:")
        print(f"  approach_area_distance: {device.approach_area_distance} (expected: {expected_distance})")
        print(f"  approach_area_seconds: {device.approach_area_seconds} (expected: {expected_seconds})")
        return False

def restore_device(device, original_values):
    """Restore the device to its original values"""
    print("\nRestoring device to original values...")

    # Restore all fields
    device.user_id = original_values['user_id']
    device.display_device_id = original_values['display_device_id']
    device.device_name = original_values['device_name']
    device.work_id = original_values['work_id']
    device.work_time = original_values['work_time']
    device.battery = original_values['battery']
    device.previous_alert_instruction = original_values['previous_alert_instruction']
    device.signal_period = original_values['signal_period']
    device.approach_area_distance = original_values['approach_area_distance']
    device.approach_area_seconds = original_values['approach_area_seconds']
    device.status = original_values['status']
    device.updated_at = timezone.now().strftime('%Y-%m-%d %H:%M:%S')

    # Save the device
    device.save()

    print("Device restored successfully.")

def main():
    """Main function"""
    print("=== Approach Area Fields Test ===")

    # Start the server
    server_process = start_server()

    try:
        # Create test user
        test_username = create_test_user()

        # Get authentication token
        token = get_auth_token(test_username, 'test_password')
        if not token:
            print("Failed to get authentication token.")
            return 1

        # Get a device from the database
        device = get_device()
        if not device:
            print("No device to test with.")
            return 1

        # Print device details
        print_device_details(device)

        # Store original values
        original_values = {
            'user_id': device.user_id,
            'display_device_id': device.display_device_id,
            'device_name': device.device_name,
            'work_id': device.work_id,
            'work_time': device.work_time,
            'battery': device.battery,
            'previous_alert_instruction': device.previous_alert_instruction,
            'signal_period': device.signal_period,
            'approach_area_distance': device.approach_area_distance,
            'approach_area_seconds': device.approach_area_seconds,
            'status': device.status,
        }

        # Prepare update data
        update_data = {
            'name': f"{device.device_name}_updated",
            'deviceId': f"{device.display_device_id}_updated",
            'signalPeriod': 180,
            'approachDistance': 8.5,
            'approachSeconds': 55,
            'approachAreaDistance': 12.5,
            'approachAreaSeconds': 75,
            'status': "No Alert"
        }

        # Update device settings through the API
        response_data = update_device_settings_api(device.id, update_data, token)
        if not response_data:
            print("Failed to update device settings through API.")
            return 1

        # Verify that the device's approach_area_distance and approach_area_seconds fields are set correctly
        success = verify_approach_area_fields(device.id, 12.5, 75)

        # Restore the device to its original values
        restore_device(device, original_values)

        # Print restored device details
        print_device_details(device)

        if success:
            print("\nTest completed successfully!")
            return 0
        else:
            print("\nTest failed.")
            return 1

    finally:
        # Stop the server
        stop_server(server_process)

if __name__ == "__main__":
    sys.exit(main())
