# LIPS Web Application

This repository contains the code for the LIPS Web Application built with Django.

## Quick Start

For a simple one-command setup process, please see the [Quick Start Guide](QUICK_START.md).

## Option 1: Using the Setup Script (Recommended)

The easiest way to run this application:

1. Make sure you have Python 3.8+ and MySQL installed
2. Run the setup script:
   - Windows: Double-click `start.bat`
   - macOS/Linux: Run `./start.sh`
3. Follow the on-screen instructions
4. Access the application at http://localhost:8000

## Option 2: Manual Setup

If you prefer to set up manually:

1. Make sure you have Python 3.8+ and MySQL installed
2. Follow the manual setup instructions in the [Quick Start Guide](QUICK_START.md#standard-manual-setup)

## Technical Details

### Database Setup

The setup scripts will automatically create the required database tables and roles. If you're setting up manually, make sure to create the following roles in this exact order:

| id  | name    |
| --- | ------- |
| 1   | admin   |
| 2   | manager |
| 3   | user    |

The application uses MySQL with the following default configuration:
- Database name: lipsweb
- Username: root
- Password: (empty)
- Host: localhost
- Port: 3307

You can customize these settings by editing the `.env` file in the project root.

### API Documentation

Once the application is running, you can access the API documentation at:

- Swagger UI: http://localhost:8000/ (root URL)
- ReDoc: http://localhost:8000/redoc/

### Main API Endpoints

- Authentication & User Management: `/api/v1/userapi/`
- LIPS API: `/api/v1/lipsapi/`
- Notification API: `/api/v1/`

### Default Admin Credentials

When using the setup scripts or Docker, a default admin user is created:

- Username: `admin`
- Password: `admin`

For security reasons, please change this password after your first login.

## Development Information

For developers who want to work on this project:

### Running Tests

```
python manage.py test
```

### Manual Server Start

```
python manage.py runserver
```

### Creating Additional Users

```
python manage.py createsuperuser
```

## Need Help?

For more detailed instructions and troubleshooting, please refer to the [Quick Start Guide](QUICK_START.md).
