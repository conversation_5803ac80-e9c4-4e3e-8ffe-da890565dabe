{"info": {"name": "Notification Alert API", "description": "API collection for the Notification Alert system which manages devices, alerts, and work operations.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "description": "Authentication endpoints for login, registration and password management", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/auth/login/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "login", ""]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"your_password\"\n}"}, "description": "Login to obtain JWT token for authentication"}, "response": []}, {"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/auth/register/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "register", ""]}, "body": {"mode": "raw", "raw": "{\n  \"full_name\": \"山田 太郎\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"your_password\",\n  \"role\": \"admin\"\n}"}, "description": "Register a new user account"}, "response": []}, {"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/auth/forgot-password/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "forgot-password", ""]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "description": "Request a password reset link"}, "response": []}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/auth/reset-password/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "reset-password", ""]}, "body": {"mode": "raw", "raw": "{\n  \"token\": \"reset_token\",\n  \"password\": \"new_password\"\n}"}, "description": "Reset password using the token received via email"}, "response": []}]}, {"name": "User", "description": "User profile management endpoints", "item": [{"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/v1/users/me/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "users", "me", ""]}, "description": "Retrieves the profile of the currently authenticated user"}, "response": []}, {"name": "Update User Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/users/me/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "users", "me", ""]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"山田 太郎\",\n  \"email\": \"<EMAIL>\"\n}"}, "description": "Updates the profile of the currently authenticated user"}, "response": []}]}, {"name": "Devices", "description": "Device management endpoints", "item": [{"name": "Get All Devices", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/v1/devices/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "devices", ""]}, "description": "Retrieves a list of all devices"}, "response": []}, {"name": "Create Device", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/devices/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "devices", ""]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"New Device\",\n  \"device_id\": \"0030\",\n  \"charge\": 100,\n  \"status\": \"normal\"\n}"}, "description": "Creates a new device"}, "response": []}, {"name": "Get Device by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/v1/devices/1/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "devices", "1", ""]}, "description": "Retrieves a specific device by ID"}, "response": []}, {"name": "Update Device", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/devices/1/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "devices", "1", ""]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Device Name\",\n  \"charge\": 85,\n  \"status\": \"normal\"\n}"}, "description": "Updates a specific device"}, "response": []}, {"name": "Delete Device", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/v1/devices/1/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "devices", "1", ""]}, "description": "Deletes a specific device"}, "response": []}, {"name": "Update Device Assignment", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/devices/1/assign/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "devices", "1", "assign", ""]}, "body": {"mode": "raw", "raw": "{\n  \"assigned_work\": \"SVLR0008\"\n}"}, "description": "Assigns or removes a work assignment for a device"}, "response": []}]}, {"name": "<PERSON><PERSON>s", "description": "Device settings management endpoints", "item": [{"name": "Get All Device Settings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/v1/devices-settings/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "devices", "settings", ""]}, "description": "Retrieves settings for all devices"}, "response": []}, {"name": "Update <PERSON><PERSON>s", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/devices-settings/1/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "devices", "settings", "1", ""]}, "body": {"mode": "raw", "raw": "{\n  \"approach_distance\": 5.5,\n  \"approach_seconds\": 35,\n  \"status\": \"active\"\n}"}, "description": "Updates settings for a specific device"}, "response": []}]}, {"name": "Works", "description": "Work/Operation management endpoints", "item": [{"name": "Get All Works", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/v1/works/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "works", ""]}, "description": "Retrieves a list of all works/operations"}, "response": []}, {"name": "Create Work", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/works/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "works", ""]}, "body": {"mode": "raw", "raw": "{\n  \"work_name\": \"SVLR0015\"\n}"}, "description": "Creates a new work/operation"}, "response": []}, {"name": "Get Work Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/v1/works/SVLR0008/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "works", "SVLR0008", ""]}, "description": "Retrieves detailed information for a specific work by work name"}, "response": []}, {"name": "Update Work", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/works/SVLR0008/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "works", "SVLR0008", ""]}, "body": {"mode": "raw", "raw": "{\n  \"work_name\": \"SVLR0008\"\n}"}, "description": "Updates a specific work"}, "response": []}, {"name": "Delete Work", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/v1/works/SVLR0008/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "works", "SVLR0008", ""]}, "description": "Deletes a specific work"}, "response": []}]}, {"name": "<PERSON><PERSON><PERSON>", "description": "Alert management endpoints", "item": [{"name": "Get All Alerts", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/v1/alerts/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "alerts", ""]}, "description": "Retrieves a list of all alerts"}, "response": []}, {"name": "Create <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/alerts/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "alerts", ""]}, "body": {"mode": "raw", "raw": "{\n  \"device\": 1,\n  \"alert_type\": \"approach\",\n  \"message\": \"Devi<PERSON> is approaching a prohibited area\",\n  \"is_read\": false\n}"}, "description": "Creates a new alert"}, "response": []}, {"name": "Get Alert by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/v1/alerts/1/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "alerts", "1", ""]}, "description": "Retrieves a specific alert by ID"}, "response": []}, {"name": "Update Al<PERSON>", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/alerts/1/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "alerts", "1", ""]}, "body": {"mode": "raw", "raw": "{\n  \"device\": 1,\n  \"alert_type\": \"approach\",\n  \"message\": \"Updated alert message\",\n  \"is_read\": false\n}"}, "description": "Updates a specific alert"}, "response": []}, {"name": "Delete Alert", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/v1/alerts/1/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "alerts", "1", ""]}, "description": "Deletes a specific alert"}, "response": []}, {"name": "<PERSON> as <PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/v1/alerts/1/mark-read/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "alerts", "1", "mark-read", ""]}, "description": "Marks a specific alert as read"}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:8000", "type": "string"}, {"key": "token", "value": "your_jwt_token_here", "type": "string"}]}