# Quick Start Guide for LIPS Web Application

This guide provides simple instructions to run the LIPS Web Application with a single command, regardless of your technical expertise.

## Choose Your Setup Method

You can run this application in two ways:

1. **Using the Setup Script** - Automated setup with a single command (recommended)
2. **Manual Setup** - Step-by-step manual configuration

## Prerequisites

- **Python 3.8 or higher** - [Download Python](https://www.python.org/downloads/)
- **MySQL Database** - [Download MySQL](https://dev.mysql.com/downloads/)

## Running the Application

### Option 1: Using the Setup Script

#### For Windows Users

1. Double-click on the `start.bat` file
2. Follow the on-screen instructions
3. The application will automatically:
   - Set up a virtual environment
   - Install all required dependencies (including PyMySQL)
   - Configure the database
   - Create the MySQL database if it doesn't exist
   - Run database migrations
   - Create necessary user roles
   - Create an admin user (if one doesn't exist)
   - Start the web server

#### For macOS/Linux Users

1. Open Terminal
2. Navigate to the project directory:
   ```
   cd path/to/lipsweb
   ```
3. Make the start script executable:
   ```
   chmod +x start.sh
   ```
4. Run the script:
   ```
   ./start.sh
   ```
5. Follow the on-screen instructions

## Accessing the Application

Once the server is running, you can access the application at:

- **Web Interface**: http://localhost:8000
- **API Documentation**: http://localhost:8000 (Swagger UI)
- **Admin Interface**: http://localhost:8000/admin

## Troubleshooting

### Common Issues

1. **Database Connection Problems**:
   - Make sure MySQL is installed and running
   - Check that the database credentials in the `.env` file are correct
   - Verify that the MySQL port (default: 3306) is not being used by another application

2. **Migration Issues**:
   - If you encounter errors during migrations, check that:
     - The database exists and is accessible
     - Your database user has the correct permissions
     - The database credentials in the .env file are correct
   - If migrations fail, you can choose to continue with the setup anyway

3. **Port Already in Use**:
   - If port 8000 is already in use, you can specify a different port:
     - For Windows: Run `start.bat 8080` (or any other port)
     - For macOS/Linux: Run `./start.sh 8080` (or any other port)

4. **Python Not Found**:
   - Make sure Python is installed and added to your system PATH
   - For Windows, try using `py` instead of `python` if you have the Python launcher installed

5. **Permission Issues**:
   - If you encounter permission errors when running the scripts, make sure they are executable:
     ```
     chmod +x start.sh
     ```

## Option 2: Manual Setup

If you prefer to set up the application manually without using the scripts, follow these steps:

1. Create and activate a virtual environment:
   ```bash
   # Create virtual environment
   python -m venv venv

   # Activate on Windows
   venv\Scripts\activate

   # Activate on macOS/Linux
   source venv/bin/activate
   ```

2. Install dependencies:
   ```bash
   # Install from requirements.txt
   pip install -r requirements.txt

   # Ensure PyMySQL is installed (required by settings.py)
   pip install PyMySQL
   ```

3. Create a `.env` file with the following content:
   ```
   DJANGO_SECRET_KEY=your_secret_key
   DEBUG=True
   DB_NAME=lipsweb
   DB_USER=root
   DB_PASSWORD=your_password
   DB_HOST=localhost
   DB_PORT=3307
   ```

4. Create the MySQL database:
   ```bash
   # Log into MySQL (replace 'root' with your MySQL username if different)
   mysql -u root -p

   # In MySQL prompt, create the database
   CREATE DATABASE lipsweb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

   # Exit MySQL
   EXIT;
   ```

   Note: Make sure the database name, username, password, host, and port match what you specified in your .env file. The default port is 3307.

5. Run migrations:
   ```bash
   # Run all migrations
   python manage.py migrate
   ```

6. Create roles (important for application functionality):
   ```bash
   # Create a temporary Python script
   echo "from django.core.management.base import BaseCommand
from usermanagement.models import Role

class Command(BaseCommand):
    help = 'Create initial roles'

    def handle(self, *args, **kwargs):
        roles = ['admin', 'manager', 'user']
        for i, role_name in enumerate(roles, 1):
            role, created = Role.objects.get_or_create(id=i, defaults={'name': role_name})
            if created:
                self.stdout.write(self.style.SUCCESS(f'Created role: {role_name}'))
            else:
                self.stdout.write(f'Role already exists: {role_name}')
" > usermanagement/management/commands/create_roles.py

   # Create the directory structure if it doesn't exist
   mkdir -p usermanagement/management/commands

   # Run the command
   python manage.py create_roles
   ```

7. Create a superuser:
   ```bash
   python manage.py createsuperuser
   # Follow the prompts to create an admin user
   ```

8. Generate an access token (optional):
   ```bash
   python generate_token.py
   ```

9. Start the server:
   ```bash
   # Standard port (8000)
   python manage.py runserver

   # Custom port (e.g., 8080)
   python manage.py runserver 8080

   # Make available on your network
   python manage.py runserver 0.0.0.0:8000
   ```

10. Access the application:
    - Web Interface: http://localhost:8000
    - API Documentation: http://localhost:8000 (Swagger UI)
    - Admin Interface: http://localhost:8000/admin
