#!/usr/bin/env python
"""
Script to check the settings in the database.
"""

import os
import sys
import django
from pprint import pprint

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Import models after Django setup
from lips.models import DeviceMaster, SettingsInfo, UserMaster

def check_settings():
    """Check all settings in the database"""
    print("Checking all settings in the database...")
    
    # Get all settings
    settings = SettingsInfo.objects.all()
    
    print(f"Found {len(settings)} settings:")
    for setting in settings:
        print(f"ID: {setting.id}, Key: {setting.key_name}, Value: {setting.value}")
    
    # Get all user masters
    users = UserMaster.objects.all()
    
    print(f"\nFound {len(users)} users:")
    for user in users:
        print(f"ID: {user.id}")
        
        # Get settings for this user
        user_settings = SettingsInfo.objects.filter(id=user.id)
        user_settings_with_suffix = SettingsInfo.objects.filter(id__startswith=f"{user.id}_")
        
        print(f"  Settings with user ID: {len(user_settings)}")
        for setting in user_settings:
            print(f"    Key: {setting.key_name}, Value: {setting.value}")
        
        print(f"  Settings with user ID prefix: {len(user_settings_with_suffix)}")
        for setting in user_settings_with_suffix:
            print(f"    ID: {setting.id}, Key: {setting.key_name}, Value: {setting.value}")

if __name__ == "__main__":
    check_settings()
