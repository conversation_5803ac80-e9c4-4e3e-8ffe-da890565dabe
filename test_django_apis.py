#!/usr/bin/env python
"""
Django Unit Tests for APIs
Uses Django's built-in testing framework
"""

import os
import sys
import django
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
import json

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from lips.models import DeviceMaster, WorkInfo, HistoricalJudgeAlertInstruction
from usermanagement.models import Role, User

class APITestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        self.client = Client()
        
        # Create test role
        self.admin_role, _ = Role.objects.get_or_create(
            name='admin',
            defaults={'description': 'Administrator role'}
        )
        
        # Create test user
        self.test_user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword123',
            full_name='Test User',
            role=self.admin_role
        )
        
        # Create test work
        self.test_work = WorkInfo.objects.create(
            work_name='TEST_WORK',
            group_num='GROUP_001'
        )
        
        # Create test device
        self.test_device = DeviceMaster.objects.create(
            device_name='Test Device',
            display_device_id='TEST001',
            battery=100,
            status='active'
        )

    def get_auth_token(self):
        """Get authentication token"""
        response = self.client.post('/api/v1/auth/login/', {
            'email': '<EMAIL>',
            'password': 'testpassword123'
        }, content_type='application/json')
        
        if response.status_code == 200:
            data = response.json()
            return data.get('access')
        return None

    def test_authentication_apis(self):
        """Test authentication endpoints"""
        print("Testing Authentication APIs...")
        
        # Test login
        response = self.client.post('/api/v1/auth/login/', {
            'email': '<EMAIL>',
            'password': 'testpassword123'
        }, content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('access', data)
        print("✅ Login API works")

    def test_device_apis(self):
        """Test device endpoints"""
        print("Testing Device APIs...")
        
        # Get auth token
        token = self.get_auth_token()
        if not token:
            self.fail("Could not get authentication token")
        
        headers = {'HTTP_AUTHORIZATION': f'Bearer {token}'}
        
        # Test get all devices
        response = self.client.get('/api/v1/lipsapi/device/', **headers)
        self.assertEqual(response.status_code, 200)
        print("✅ Get All Devices API works")
        
        # Test create device
        device_data = {
            'device_name': 'New Test Device',
            'display_device_id': 'NEW001',
            'battery': 90,
            'status': 'active'
        }
        response = self.client.post('/api/v1/lipsapi/device/', 
                                  json.dumps(device_data),
                                  content_type='application/json',
                                  **headers)
        self.assertIn(response.status_code, [200, 201])
        print("✅ Create Device API works")

    def test_works_apis(self):
        """Test work endpoints"""
        print("Testing Works APIs...")
        
        # Get auth token
        token = self.get_auth_token()
        if not token:
            self.fail("Could not get authentication token")
        
        headers = {'HTTP_AUTHORIZATION': f'Bearer {token}'}
        
        # Test get all works
        response = self.client.get('/api/v1/lipsapi/works/', **headers)
        self.assertEqual(response.status_code, 200)
        print("✅ Get All Works API works")
        
        # Test create work
        work_data = {
            'work_name': 'NEW_TEST_WORK',
            'group_num': 'NEW_GROUP'
        }
        response = self.client.post('/api/v1/lipsapi/works/',
                                  json.dumps(work_data),
                                  content_type='application/json',
                                  **headers)
        self.assertIn(response.status_code, [200, 201])
        print("✅ Create Work API works")

    def test_alert_apis(self):
        """Test alert endpoints"""
        print("Testing Alert APIs...")
        
        # Get auth token
        token = self.get_auth_token()
        if not token:
            self.fail("Could not get authentication token")
        
        headers = {'HTTP_AUTHORIZATION': f'Bearer {token}'}
        
        # Test get all alerts
        response = self.client.get('/api/v1/lipsapi/alerts/', **headers)
        self.assertEqual(response.status_code, 200)
        print("✅ Get All Alerts API works")
        
        # Test create alert
        alert_data = {
            'device': self.test_device.id,
            'alert_type': 'approach',
            'message': 'Test alert message',
            'is_read': False
        }
        response = self.client.post('/api/v1/lipsapi/alerts/',
                                  json.dumps(alert_data),
                                  content_type='application/json',
                                  **headers)
        self.assertIn(response.status_code, [200, 201])
        print("✅ Create Alert API works")

    def test_device_settings_apis(self):
        """Test device settings endpoints"""
        print("Testing Device Settings APIs...")
        
        # Get auth token
        token = self.get_auth_token()
        if not token:
            self.fail("Could not get authentication token")
        
        headers = {'HTTP_AUTHORIZATION': f'Bearer {token}'}
        
        # Test get all device settings
        response = self.client.get('/api/v1/lipsapi/device/settings/', **headers)
        self.assertEqual(response.status_code, 200)
        print("✅ Get All Device Settings API works")

def run_django_tests():
    """Run Django unit tests"""
    print("🧪 Running Django Unit Tests...")
    print("=" * 50)
    
    # Create test suite
    from django.test.utils import get_runner
    from django.conf import settings
    
    # Configure test settings
    settings.DATABASES['default']['NAME'] = ':memory:'
    
    # Get test runner
    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    
    # Run tests
    test_case = APITestCase()
    test_case.setUp()
    
    try:
        test_case.test_authentication_apis()
        test_case.test_device_apis()
        test_case.test_works_apis()
        test_case.test_alert_apis()
        test_case.test_device_settings_apis()
        
        print("\n✅ All Django unit tests passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Django unit tests failed: {e}")
        return False

if __name__ == "__main__":
    success = run_django_tests()
    sys.exit(0 if success else 1)
