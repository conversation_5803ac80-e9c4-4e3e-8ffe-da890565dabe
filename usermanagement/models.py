from django import forms
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _

class Role(models.Model):
    name = models.CharField(max_length=50, unique=True)
    objects = models.Manager()

    def __str__(self):
        return self.name

    @staticmethod
    def initialize_roles():
        roles = ['admin', 'manager', 'user']
        for role_name in roles:
            Role.objects.get_or_create(name=role_name)


class User(AbstractUser):
    role = models.ForeignKey(
        Role, on_delete=models.SET_NULL,
        null=True, blank=True
        )
    email = models.EmailField(_("email address"), unique=True)
    full_name = models.CharField(
        max_length=255, null=True,
        blank=True
        )
    status = models.CharField(
        max_length=50, default='active'
        )
    phone_number = models.Char<PERSON>ield(
        max_length=20, blank=True, null=True
        )
    notes = models.TextField(
        blank=True, null=True
        )
    otp = models.CharField(
        max_length=6, blank=True, null=True
        )
    otp_expired_at = models.DateTimeField(
        blank=True, null=True
        )
    company_name = models.CharField(max_length=255, blank=True)

    def __str__(self):
        return self.username


class UserForm(forms.ModelForm):
    class Meta:
        model = User
        fields = '__all__'
