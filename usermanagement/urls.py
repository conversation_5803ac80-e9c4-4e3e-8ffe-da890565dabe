from django.urls import path
from rest_framework_simplejwt.views import (TokenObtainPairView,
                                            TokenRefreshView)

from .views import (AddUser, ChangePassword, DeleteUser, ForgotPassword,
                    GetAllUsers, LoginUser, RegisterUser, ResetPassword,
                    UpdateUser, AddUserWithDevice, UpdateUserDeviceOnly, RemoveUserDevice)

urlpatterns = [
    path('token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('register/', RegisterUser.as_view(), name='register'),
    path('login/', LoginUser.as_view(), name='login'),
    path('forgot-password/', ForgotPassword.as_view(), name='forgot-password'),
    path('reset-password/', ResetPassword.as_view(), name='reset-password'),
    path('change-password/', ChangePassword.as_view(), name='change-password'),
    path('users/list/', GetAllUsers.as_view(), name='get-all-users'),
    path('users/add/', AddUserWithDevice.as_view(), name='add_user_with_device'),
    path('users/update/<int:id>/', UpdateUserDeviceOnly.as_view(), name='update_user_device'),
    path('users/<int:id>/device/', RemoveUserDevice.as_view(), name='remove_user_device'),
    # Keep original endpoints for backward compatibility
    path('users/add-original/', AddUser.as_view(), name='add_user_original'),
    path('users/update-original/<int:id>/', UpdateUser.as_view(), name='update-user-original'),
    path('users/<int:id>/delete/', DeleteUser.as_view(), name='delete-user'),
]