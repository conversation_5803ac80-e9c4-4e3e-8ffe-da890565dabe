from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from django.utils.timezone import now, timedelta
import json

from .models import User, Role

class UserModelTest(TestCase):
    def setUp(self):
        Role.objects.get_or_create(name='admin')
        Role.objects.get_or_create(name='manager')
        Role.objects.get_or_create(name='user')

    def test_notes_field(self):
        """Test that the notes field works correctly"""
        # Use a unique username for testing
        username = 'testuser_notes_field'

        # Delete the user if it already exists
        User.objects.filter(username=username).delete()

        user = User.objects.create_user(
            username=username,
            email='<EMAIL>',
            password='password123',
            notes='Test notes'
        )
        self.assertEqual(user.notes, 'Test notes')

        # Update notes
        user.notes = 'Updated notes'
        user.save()

        # Refresh from database
        user.refresh_from_db()
        self.assertEqual(user.notes, 'Updated notes')


class UserAPITest(TestCase):
    def setUp(self):
        self.client = APIClient()

        # Create roles
        self.admin_role, _ = Role.objects.get_or_create(name='admin')
        self.manager_role, _ = Role.objects.get_or_create(name='manager')
        self.user_role, _ = Role.objects.get_or_create(name='user')

        # Create test users with unique usernames
        self.admin_user = User.objects.create_user(
            username='admin_test_user',
            email='<EMAIL>',
            password='password123',
            notes='Admin notes',
            role=self.admin_role,
            is_active=True
        )

        self.regular_user = User.objects.create_user(
            username='regular_test_user',
            email='<EMAIL>',
            password='password123',
            notes='User notes',
            role=self.user_role,
            is_active=True
        )

        # Create a user with empty notes
        self.empty_notes_user = User.objects.create_user(
            username='empty_notes_user',
            email='<EMAIL>',
            password='password123',
            notes='',
            role=self.user_role,
            is_active=True
        )

        # Create a user with null notes
        self.null_notes_user = User.objects.create_user(
            username='null_notes_user',
            email='<EMAIL>',
            password='password123',
            notes=None,
            role=self.user_role,
            is_active=True
        )

        # Create a user with long notes
        self.long_notes_user = User.objects.create_user(
            username='long_notes_user',
            email='<EMAIL>',
            password='password123',
            notes='This is a very long note that contains multiple sentences. It should test the ability of the system to handle longer text content in the notes field. This simulates a real-world scenario where users might enter detailed information about themselves or their preferences. The system should be able to store and retrieve this information correctly.',
            role=self.user_role,
            is_active=True
        )

        # Generate auth token for admin user
        login_url = reverse('login')
        login_data = {
            'email': '<EMAIL>',
            'password': 'password123'
        }
        login_response = self.client.post(login_url, login_data, format='json')
        self.admin_token = login_response.data['access_token']

        # Generate auth token for regular user
        login_data = {
            'email': '<EMAIL>',
            'password': 'password123'
        }
        login_response = self.client.post(login_url, login_data, format='json')
        self.user_token = login_response.data['access_token']

    def test_register_user(self):
        """Test registering a new user with notes field"""
        url = reverse('register')
        data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'password': 'password123',
            'full_name': 'New User',
            'notes': 'New user notes',
            'phone_number': '1234567890',
            'company_name': 'Test Company'
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['data']['notes'], 'New user notes')

        # Verify user was created in database
        user = User.objects.get(email='<EMAIL>')
        self.assertEqual(user.notes, 'New user notes')

    def test_login_user(self):
        """Test login returns user data with notes field"""
        url = reverse('login')
        data = {
            'email': '<EMAIL>',
            'password': 'password123'
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['notes'], 'Admin notes')

    def test_get_all_users(self):
        """Test getting all users includes notes field"""
        url = reverse('get-all-users')
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that notes field is included for each user
        for user in response.data['data']:
            self.assertIn('notes', user)

    def test_add_user(self):
        """Test adding a user with notes field"""
        url = reverse('add_user')
        data = {
            'username': 'addeduser',
            'email': '<EMAIL>',
            'password': 'password123',
            'full_name': 'Added User',
            'notes': 'Added user notes',
            'phone_number': '9876543210',
            'company_name': 'Added Company',
            'role_id': self.user_role.id
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['data']['notes'], 'Added user notes')

        # Verify user was created in database
        user = User.objects.get(email='<EMAIL>')
        self.assertEqual(user.notes, 'Added user notes')

    def test_update_user(self):
        """Test updating a user's notes field"""
        url = reverse('update-user', args=[self.regular_user.id])
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        data = {
            'notes': 'Updated user notes'
        }
        response = self.client.put(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['notes'], 'Updated user notes')

        # Verify user was updated in database
        self.regular_user.refresh_from_db()
        self.assertEqual(self.regular_user.notes, 'Updated user notes')

    def test_delete_user(self):
        """Test deleting a user"""
        # Create a user to delete
        user_to_delete = User.objects.create_user(
            username='delete_me',
            email='<EMAIL>',
            password='password123',
            notes='Delete notes'
        )

        url = reverse('delete-user', args=[user_to_delete.id])
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify user was deleted from database
        with self.assertRaises(User.DoesNotExist):
            User.objects.get(id=user_to_delete.id)

    def test_forgot_password(self):
        """Test forgot password functionality"""
        # This test is limited since we can't easily check email sending
        url = reverse('forgot-password')
        data = {
            'email': '<EMAIL>'
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify OTP was generated
        self.regular_user.refresh_from_db()
        self.assertIsNotNone(self.regular_user.otp)
        self.assertIsNotNone(self.regular_user.otp_expired_at)

    def test_reset_password(self):
        """Test reset password functionality"""
        # First set up an OTP
        self.regular_user.otp = '123456'
        self.regular_user.otp_expired_at = now() + timedelta(minutes=10)
        self.regular_user.save()

        url = reverse('reset-password')
        data = {
            'email': '<EMAIL>',
            'otp': '123456',
            'password': 'newpassword123'
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify password was changed
        self.regular_user.refresh_from_db()
        self.assertTrue(self.regular_user.check_password('newpassword123'))
        self.assertIsNone(self.regular_user.otp)
        self.assertIsNone(self.regular_user.otp_expired_at)

    def test_change_password(self):
        """Test change password functionality"""
        url = reverse('change-password')
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.user_token}')
        data = {
            'email': '<EMAIL>',
            'password': 'password123',
            'new_password': 'changedpassword123'
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify password was changed
        self.regular_user.refresh_from_db()
        self.assertTrue(self.regular_user.check_password('changedpassword123'))

    # Additional tests specifically for the notes field
    def test_empty_notes_field(self):
        """Test that empty notes field works correctly"""
        # Login with empty notes user
        url = reverse('login')
        data = {
            'email': '<EMAIL>',
            'password': 'password123'
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('notes', response.data['data'])
        self.assertEqual(response.data['data']['notes'], '')

    def test_null_notes_field(self):
        """Test that null notes field works correctly"""
        # Login with null notes user
        url = reverse('login')
        data = {
            'email': '<EMAIL>',
            'password': 'password123'
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('notes', response.data['data'])
        # Django REST Framework typically converts None to null in JSON
        self.assertIsNone(response.data['data']['notes'])

    def test_long_notes_field(self):
        """Test that long notes field works correctly"""
        # Login with long notes user
        url = reverse('login')
        data = {
            'email': '<EMAIL>',
            'password': 'password123'
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('notes', response.data['data'])
        # Verify the long notes are returned correctly
        expected_notes = 'This is a very long note that contains multiple sentences. It should test the ability of the system to handle longer text content in the notes field. This simulates a real-world scenario where users might enter detailed information about themselves or their preferences. The system should be able to store and retrieve this information correctly.'
        self.assertEqual(response.data['data']['notes'], expected_notes)

    def test_update_notes_to_empty(self):
        """Test updating notes to empty string"""
        url = reverse('update-user', args=[self.regular_user.id])
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        data = {
            'notes': ''
        }
        response = self.client.put(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['notes'], '')

        # Verify user was updated in database
        self.regular_user.refresh_from_db()
        self.assertEqual(self.regular_user.notes, '')

    def test_update_notes_to_null(self):
        """Test updating notes to null"""
        url = reverse('update-user', args=[self.regular_user.id])
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        data = {
            'notes': None
        }
        response = self.client.put(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsNone(response.data['data']['notes'])

        # Verify user was updated in database
        self.regular_user.refresh_from_db()
        self.assertIsNone(self.regular_user.notes)

    def test_update_notes_with_special_characters(self):
        """Test updating notes with special characters"""
        url = reverse('update-user', args=[self.regular_user.id])
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        special_notes = "Special characters: !@#$%^&*()_+{}[]|\\:;\"'<>,.?/~`"
        data = {
            'notes': special_notes
        }
        response = self.client.put(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['notes'], special_notes)

        # Verify user was updated in database
        self.regular_user.refresh_from_db()
        self.assertEqual(self.regular_user.notes, special_notes)

    def test_register_with_html_in_notes(self):
        """Test registering a user with HTML in notes field"""
        url = reverse('register')
        html_notes = "<script>alert('test');</script><p>This is a paragraph</p>"
        data = {
            'username': 'html_notes_user',
            'email': '<EMAIL>',
            'password': 'password123',
            'full_name': 'HTML User',
            'notes': html_notes,
            'phone_number': '1234567890',
            'company_name': 'Test Company'
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['data']['notes'], html_notes)

        # Verify user was created in database
        user = User.objects.get(email='<EMAIL>')
        self.assertEqual(user.notes, html_notes)

    def test_notes_in_all_users_endpoint(self):
        """Test that notes field is included for all users in the get all users endpoint"""
        url = reverse('get-all-users')
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.admin_token}')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check that notes field is included for each user type
        user_emails = {
            '<EMAIL>': 'Admin notes',
            '<EMAIL>': 'User notes',
            '<EMAIL>': '',
            '<EMAIL>': None,
            '<EMAIL>': 'This is a very long note that contains multiple sentences. It should test the ability of the system to handle longer text content in the notes field. This simulates a real-world scenario where users might enter detailed information about themselves or their preferences. The system should be able to store and retrieve this information correctly.'
        }

        for user in response.data['data']:
            if user['email'] in user_emails:
                self.assertIn('notes', user)
                expected_notes = user_emails[user['email']]
                if expected_notes is None:
                    self.assertIsNone(user['notes'])
                else:
                    self.assertEqual(user['notes'], expected_notes)

    def test_regular_user_can_update_own_notes(self):
        """Test that a regular user can update their own notes"""
        # Get the regular user's ID
        url = reverse('update-user', args=[self.regular_user.id])

        # Use the regular user's token
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.user_token}')

        # Update the notes
        new_notes = "Updated by the user themselves"
        data = {
            'notes': new_notes
        }
        response = self.client.put(url, data, format='json')

        # Check the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['notes'], new_notes)

        # Verify user was updated in database
        self.regular_user.refresh_from_db()
        self.assertEqual(self.regular_user.notes, new_notes)

    def test_regular_user_cannot_update_other_users_notes(self):
        """Test that a regular user cannot update another user's notes"""
        # Try to update the admin user's notes using the regular user's token
        url = reverse('update-user', args=[self.admin_user.id])

        # Use the regular user's token
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.user_token}')

        # Attempt to update the notes
        data = {
            'notes': "This should not work"
        }
        response = self.client.put(url, data, format='json')

        # Check that the request was denied
        self.assertNotEqual(response.status_code, status.HTTP_200_OK)

        # Verify admin user's notes were not changed
        self.admin_user.refresh_from_db()
        self.assertEqual(self.admin_user.notes, 'Admin notes')
