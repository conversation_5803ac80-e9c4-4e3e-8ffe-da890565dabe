from django.core.management.base import BaseCommand
from usermanagement.models import Role

class Command(BaseCommand):
    help = 'Create initial roles'

    def handle(self, *args, **kwargs):
        roles = ['admin', 'manager', 'user']
        for i, role_name in enumerate(roles, 1):
            role, created = Role.objects.get_or_create(id=i, defaults={'name': role_name})
            if created:
                self.stdout.write(self.style.SUCCESS(f'Created role: {role_name}'))
            else:
                self.stdout.write(f'Role already exists: {role_name}')
