
from django.core.management.base import BaseCommand
from usermanagement.models import User, Role
from django.contrib.auth.hashers import make_password

class Command(BaseCommand):
    help = 'Create a superuser if one does not exist'

    def add_arguments(self, parser):
        parser.add_argument('--username', type=str, help='Superuser username')
        parser.add_argument('--password', type=str, help='Superuser password')
        parser.add_argument('--email', type=str, help='Superuser email')

    def handle(self, *args, **kwargs):
        if User.objects.filter(role__name='admin').exists():
            self.stdout.write('Admin user already exists')
            return

        username = kwargs['username']
        password = kwargs['password']
        email = kwargs['email']

        admin_role, _ = Role.objects.get_or_create(name='admin')

        User.objects.create(
            username=username,
            password=make_password(password),
            email=email,
            role=admin_role,
            full_name='Admin User',
            status=True
        )

        self.stdout.write(self.style.SUCCESS(f'Superuser {username} created successfully'))
