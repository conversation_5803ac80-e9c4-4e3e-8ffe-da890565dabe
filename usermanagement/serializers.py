import random

from django.contrib.auth import authenticate
from django.contrib.auth.hashers import make_password
from django.utils.timezone import now
from rest_framework import serializers

from .models import Role, User


class RoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Role
        fields = '__all__'


class UserSerializer(serializers.ModelSerializer):
    role = RoleSerializer(read_only=True)
    role_name = serializers.CharField(write_only=True, required=False)
    device_id = serializers.CharField(required=False, help_text="Device ID to assign to user")

    class Meta:
        model = User
        fields = [
            'id',
            'username',
            'email',
            'role',
            'role_name',  
            'full_name',
            'status',
            'phone_number',
            'notes',
            'company_name',
            'device_id',
            'last_login',
        ]
    
    def update(self, instance, validated_data):
        # Handle device assignment
        device_id = validated_data.pop('device_id', None)
        if device_id is not None:
            # Import here to avoid circular imports
            from lips.models import DeviceMaster
            try:
                device = DeviceMaster.objects.get(id=device_id)
                # Create or get UserMaster record
                from lips.models import UserMaster
                user_master, created = UserMaster.objects.get_or_create(
                    id=instance.username,
                    defaults={
                        'created_at': now().strftime('%Y-%m-%d %H:%M:%S'),
                        'updated_at': now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                )
                # Assign device to user
                device.user_id = user_master
                device.save()
            except DeviceMaster.DoesNotExist:
                raise serializers.ValidationError(
                    {"device_id": f"Device with ID '{device_id}' not found"}
                )
        
        # Handle role update by name if provided
        role_name = validated_data.pop('role_name', None)
        if role_name:
            try:
                # Find the role with this name and connect user to it
                role = Role.objects.get(name=role_name)
                instance.role = role
                instance.save(update_fields=['role'])
            except Role.DoesNotExist as exc:
                raise serializers.ValidationError(
                    {"role_name": f"Role '{role_name}' not found. Available roles: admin, manager, user"}
                ) from exc
        
        # Update other user fields
        return super().update(instance, validated_data)

    def to_representation(self, instance):
        """Add device_id to the response if user has an assigned device"""
        representation = super().to_representation(instance)
        
        # Get assigned device if any
        try:
            from lips.models import DeviceMaster, UserMaster
            user_master = UserMaster.objects.get(id=instance.username)
            device = DeviceMaster.objects.filter(user_id=user_master).first()
            representation['device_id'] = str(device.id) if device else None
        except:
            representation['device_id'] = None
            
        return representation


class UserCreateSerializer(serializers.ModelSerializer):
    device_id = serializers.CharField(required=False, help_text="Device ID to assign to user")
    
    class Meta:
        model = User
        fields = [
            'id',
            'username',
            'email',
            'password',
            'role',
            'full_name',
            'status',
            'phone_number',
            'notes',
            'company_name',
            'device_id'
            ]
        extra_kwargs = {'password': {'write_only': True}}

    def create(self, validated_data):
        device_id = validated_data.pop('device_id', None)
        user = User.objects.create_user(**validated_data)
        
        # Handle device assignment if provided
        if device_id:
            try:
                from lips.models import DeviceMaster, UserMaster
                device = DeviceMaster.objects.get(id=device_id)
                # Create UserMaster record for this user
                user_master = UserMaster.objects.create(
                    id=user.username,
                    created_at=now().strftime('%Y-%m-%d %H:%M:%S'),
                    updated_at=now().strftime('%Y-%m-%d %H:%M:%S')
                )
                # Assign device to user
                device.user_id = user_master
                device.save()
            except DeviceMaster.DoesNotExist:
                # If device doesn't exist, still create the user but log the error
                pass
                
        return user


class LoginSerializer(serializers.Serializer):
    email = serializers.EmailField()
    password = serializers.CharField()


class ForgotPasswordSerializer(serializers.Serializer):
    email = serializers.EmailField()

    def validate_email(self, value):
        if not User.objects.filter(email=value).exists():
            raise serializers.ValidationError("User with this email does not exist.")
        return value


class ResetPasswordSerializer(serializers.Serializer):
    email = serializers.EmailField()
    otp = serializers.CharField(max_length=6)
    password = serializers.CharField(write_only=True)

    def validate(self, data):
        try:
            user = User.objects.get(email=data["email"])
        except User.DoesNotExist:
            raise serializers.ValidationError({"email": "Invalid email address."})

        if user.otp != data["otp"]:
            raise serializers.ValidationError({"otp": "Invalid OTP."})

        if user.otp_expired_at and user.otp_expired_at < now():
            raise serializers.ValidationError({"otp": "OTP has expired."})

        return data


class ChangePasswordSerializer(serializers.Serializer):
    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)
    new_password = serializers.CharField(write_only=True)

    def validate(self, data):
        try:
            user = User.objects.get(email=data["email"])
        except User.DoesNotExist:
            raise serializers.ValidationError({"email": "User with this email does not exist."})

        if not user.check_password(data["password"]):
            raise serializers.ValidationError({"password": "Current password is incorrect."})

        return data


class AddUserSerializer(serializers.ModelSerializer):
    role_id = serializers.PrimaryKeyRelatedField(
        queryset=Role.objects.all(), source='role', write_only=True, required=False
    )  # Accept role ID but optional
    device_id = serializers.CharField(required=False, help_text="Device ID to assign to user")

    class Meta:
        model = User
        fields = [
            "id", "username", "email", "password", "full_name", "status",
            "phone_number", "notes", "company_name", "role", "role_id",
            "otp", "otp_expired_at", "device_id"
        ]
        extra_kwargs = {
            "password": {"write_only": True},  # Hide password in response
        }

    def create(self, validated_data):
        device_id = validated_data.pop('device_id', None)
        validated_data["password"] = make_password(validated_data["password"])  # Hash password
        user = super().create(validated_data)
        
        # Handle device assignment if provided
        if device_id:
            try:
                from lips.models import DeviceMaster, UserMaster
                device = DeviceMaster.objects.get(id=device_id)
                # Create UserMaster record for this user
                user_master = UserMaster.objects.create(
                    id=user.username,
                    created_at=now().strftime('%Y-%m-%d %H:%M:%S'),
                    updated_at=now().strftime('%Y-%m-%d %H:%M:%S')
                )
                # Assign device to user
                device.user_id = user_master
                device.save()
            except DeviceMaster.DoesNotExist:
                # If device doesn't exist, still create the user but log the error
                pass
                
        return user

    def to_representation(self, instance):
        """Add device_id to the response if user has an assigned device"""
        representation = super().to_representation(instance)
        
        # Get assigned device if any
        try:
            from lips.models import DeviceMaster, UserMaster
            user_master = UserMaster.objects.get(id=instance.username)
            device = DeviceMaster.objects.filter(user_id=user_master).first()
            representation['device_id'] = str(device.id) if device else None
        except:
            representation['device_id'] = None
            
        return representation


class UserDeviceCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating user with device assignment - matches Postman collection format"""
    role = serializers.IntegerField(help_text="Role ID (1=admin, 2=manager, 3=user)")
    device_id = serializers.CharField(required=True, help_text="Device ID to assign to user")

    class Meta:
        model = User
        fields = [
            'full_name',
            'username',
            'email',
            'password',
            'phone_number',
            'role',
            'company_name',
            'notes',
            'device_id'
        ]
        extra_kwargs = {'password': {'write_only': True}}

    def create(self, validated_data):
        device_id = validated_data.pop('device_id')
        role_id = validated_data.pop('role')

        # Get role by ID
        try:
            role = Role.objects.get(id=role_id)
            validated_data['role'] = role
        except Role.DoesNotExist:
            raise serializers.ValidationError(
                {"role": f"Role with ID '{role_id}' not found. Available: 1=admin, 2=manager, 3=user"}
            )

        # Create user
        user = User.objects.create_user(**validated_data)

        # Handle device assignment
        try:
            from lips.models import DeviceMaster, UserMaster
            device = DeviceMaster.objects.get(display_device_id=device_id)

            # Create UserMaster record for this user
            user_master = UserMaster.objects.create(
                id=user.username,
                created_at=now().strftime('%Y-%m-%d %H:%M:%S'),
                updated_at=now().strftime('%Y-%m-%d %H:%M:%S')
            )

            # Assign device to user
            device.user_id = user_master
            device.save()

        except DeviceMaster.DoesNotExist:
            raise serializers.ValidationError(
                {"device_id": f"Device with ID '{device_id}' not found"}
            )

        return user

    def to_representation(self, instance):
        """Return user data with device assignment info"""
        # Get the base representation but exclude role field to avoid serialization issues
        data = {
            'id': instance.id,
            'full_name': instance.full_name,
            'username': instance.username,
            'email': instance.email,
            'phone_number': instance.phone_number,
            'company_name': instance.company_name,
            'notes': instance.notes,
            'status': instance.status,
            'last_login': instance.last_login,
        }

        # Add role info
        if instance.role:
            data['role'] = {
                'id': instance.role.id,
                'name': instance.role.name
            }
        else:
            data['role'] = None

        # Get assigned device if any
        try:
            from lips.models import DeviceMaster, UserMaster
            user_master = UserMaster.objects.get(id=instance.username)
            device = DeviceMaster.objects.filter(user_id=user_master).first()
            data['device_id'] = device.display_device_id if device else None
        except:
            data['device_id'] = None

        return data


class UserDeviceUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating user device assignment only"""
    device_id = serializers.CharField(required=True, help_text="Device ID to assign to user")

    class Meta:
        model = User
        fields = ['device_id']

    def update(self, instance, validated_data):
        device_id = validated_data.get('device_id')

        if device_id:
            try:
                from lips.models import DeviceMaster, UserMaster
                device = DeviceMaster.objects.get(display_device_id=device_id)

                # Get or create UserMaster record
                user_master, created = UserMaster.objects.get_or_create(
                    id=instance.username,
                    defaults={
                        'created_at': now().strftime('%Y-%m-%d %H:%M:%S'),
                        'updated_at': now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                )

                # Remove device from previous user if any
                DeviceMaster.objects.filter(user_id=user_master).update(user_id=None)

                # Assign device to this user
                device.user_id = user_master
                device.save()

            except DeviceMaster.DoesNotExist:
                raise serializers.ValidationError(
                    {"device_id": f"Device with ID '{device_id}' not found"}
                )

        return instance

    def to_representation(self, instance):
        """Return user data with updated device assignment"""
        representation = super().to_representation(instance)

        # Get assigned device
        try:
            from lips.models import DeviceMaster, UserMaster
            user_master = UserMaster.objects.get(id=instance.username)
            device = DeviceMaster.objects.filter(user_id=user_master).first()
            representation['device_id'] = device.display_device_id if device else None
        except:
            representation['device_id'] = None

        return representation