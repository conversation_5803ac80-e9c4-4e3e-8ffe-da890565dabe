import random

from django.contrib.auth import authenticate, login
from django.contrib.auth.hashers import make_password
from django.core.exceptions import ObjectDoesNotExist
from django.core.mail import send_mail
from django.utils.timezone import now, timedelta
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
# User = get_user_model()
from rest_framework_simplejwt.tokens import RefreshToken

from .models import Role, User
from .serializers import (AddUserSerializer, ChangePasswordSerializer,
                          ForgotPasswordSerializer, LoginSerializer,
                          ResetPasswordSerializer, UserCreateSerializer,
                          UserSerializer, UserDeviceCreateSerializer,
                          UserDeviceUpdateSerializer)


class RegisterUser(APIView):
    @swagger_auto_schema(
        operation_summary="Register a new user",
        operation_description="Creates a new user account. The ID field is auto-generated and should NOT be included in the request.",
        request_body=UserCreateSerializer,
        responses={
            201: "User created successfully",
            400: "Invalid input"
        }
    )
    def post(self, request):
        serializer = UserCreateSerializer(data=request.data)
        if serializer.is_valid():
            if 'role' not in serializer.validated_data:
                user_role = Role.objects.get(name='User')
                serializer.validated_data['role'] = user_role
            serializer.save()
            return Response(
                {"status": True, "message": "User created successfully", "data": serializer.data},
                status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)



class LoginUser(APIView):
    @swagger_auto_schema(request_body=LoginSerializer)
    def post(self, request):
        serializer = LoginSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            password = serializer.validated_data['password']

            print(f"Login attempt for email: {email}")

            # Check if user exists
            user_exists = User.objects.filter(email=email).exists()
            print(f"User exists: {user_exists}")

            if not user_exists:
                print(f"User with email {email} does not exist")
                return Response(
                    {"status": False, "message": "User does not exist", "detail": "No user found with this email"},
                    status=status.HTTP_401_UNAUTHORIZED
                )

            # Manually authenticate the user using email
            try:
                user = User.objects.get(email=email)
                print(f"Found user: {user.username}, Active: {user.is_active}")

                # Check password
                password_valid = user.check_password(password)
                print(f"Password valid: {password_valid}")

                if password_valid:
                    if user.is_active:
                        refresh = RefreshToken.for_user(user)
                        access_token = str(refresh.access_token)
                        refresh_token = str(refresh)
                        login(request, user)  # Optional: Only for session-based authentication
                        role_data = None
                        if user.role:
                            role_data = {"id": user.role.id, "name": user.role.name}
                            print(f"User role: {user.role.name}")
                        else:
                            print("User has no role assigned")

                        return Response(
                            {
                                "status": True,
                                "message": "Login successful",
                                "access_token": access_token,
                                "refresh_token": refresh_token,
                                "data": {
                                    "id": user.id,
                                    "full_name": user.full_name,
                                    "email": user.email,
                                    "role": role_data,
                                    "username": user.username,
                                    "notes": user.notes,  # Changed from address to notes
                                    "company_name": user.company_name,
                                    "status": user.status,
                                    "phone_number": user.phone_number,
                                    "last_login": user.last_login,
                                },
                            }
                        )
                    else:
                        print("User is not active")
                        return Response(
                            {"status": False, "message": "User is not active", "detail": "Account is disabled"},
                            status=status.HTTP_401_UNAUTHORIZED
                        )
                else:
                    print("Invalid password")
                    return Response(
                        {"status": False, "message": "Invalid credentials", "detail": "Password is incorrect"},
                        status=status.HTTP_401_UNAUTHORIZED
                    )
            except ObjectDoesNotExist:
                print("User does not exist (exception)")
                return Response(
                    {"status": False, "message": "User does not exist", "detail": "No user found with this email"},
                    status=status.HTTP_401_UNAUTHORIZED
                )
            except Exception as e:
                print(f"Unexpected error: {str(e)}")
                return Response(
                    {"status": False, "message": "Login failed", "detail": str(e)},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        print(f"Serializer errors: {serializer.errors}")
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class GetAllUsers(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        users = User.objects.all()
        serializer = UserSerializer(users, many=True)
        return Response(
            {"status": True, "message": "Users fetched successfully", "data": serializer.data})


class ForgotPassword(APIView):
    @swagger_auto_schema(request_body=ForgotPasswordSerializer)
    def post(self, request):
        serializer = ForgotPasswordSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            user = User.objects.get(email=email)
            # Generate OTP
            otp = str(random.randint(100000, 999999))
            user.otp = otp
            print(otp, 'otp-----')
            user.otp_expired_at = now() + timedelta(minutes=10)  # OTP valid for 10 minutes
            user.save()

            # Send OTP via email
            send_mail(
                subject="Your Password Reset OTP",
                message=f"Your OTP for password reset is: {otp}. It will expire in 10 minutes.",
                from_email="<EMAIL>",
                recipient_list=[user.email],
                fail_silently=False,
            )
            print('works ---')


            return Response({"status": True, "message": "OTP sent successfully"})
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ResetPassword(APIView):
    @swagger_auto_schema(request_body=ResetPasswordSerializer)
    def post(self, request):
        serializer = ResetPasswordSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            password = serializer.validated_data['password']

            user = User.objects.get(email=email)
            user.password = make_password(password)
            user.otp = None
            user.otp_expired_at = None
            user.save()

            return Response({"status": True, "message": "Password reset successfully"})
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class AddUser(APIView):
    permission_classes = [AllowAny]

    @swagger_auto_schema(
        operation_summary="Add a new user",
        operation_description="Creates a new user. The ID field is auto-generated and should NOT be included in the request.",
        request_body=AddUserSerializer,
        responses={
            201: "User created successfully",
            400: "Invalid input"
        }
    )
    def post(self, request):
        serializer = AddUserSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"status": True, "message": "User created successfully", "data": serializer.data},
                status=status.HTTP_201_CREATED
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UpdateUser(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(request_body=UserSerializer)
    def put(self, request, id):
        try:
            user = User.objects.get(id=id)
        except ObjectDoesNotExist:
            return Response(
                {"status": False, "message": "User not found"},
                status=status.HTTP_404_NOT_FOUND)

        # Check if the user is updating their own profile or has admin/manager role
        if request.user.id != user.id and (not request.user.role or request.user.role.name.lower() not in ['admin', 'manager']):
            return Response(
                {"status": False, "message": "You don't have permission to update this user"},
                status=status.HTTP_403_FORBIDDEN)

        serializer = UserSerializer(user, data=request.data, partial=True)

        if serializer.is_valid():
            serializer.save()
            return Response(
                {"status": True, "message": "User updated successfully", "data": serializer.data})
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class DeleteUser(APIView):
    permission_classes = [IsAuthenticated]

    def delete(self, request, id):
        try:
            user = User.objects.get(id=id)
        except ObjectDoesNotExist:
            return Response(
                {"status": False, "message": "User not found"},
                status=status.HTTP_404_NOT_FOUND)

        # Only admin or manager can delete users
        if not request.user.role or request.user.role.name.lower() not in ['admin', 'manager']:
            return Response(
                {"status": False, "message": "You don't have permission to delete users"},
                status=status.HTTP_403_FORBIDDEN)

        user.delete()
        return Response({"status": True, "message": "User deleted successfully"})


class ChangePassword(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(request_body=ChangePasswordSerializer)
    def post(self, request):
        serializer = ChangePasswordSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            new_password = serializer.validated_data['new_password']

            # Check if the user is changing their own password or has admin role
            if request.user.email != email and (not request.user.role or request.user.role.name.lower() != 'admin'):
                return Response(
                    {"status": False, "message": "You don't have permission to change this user's password"},
                    status=status.HTTP_403_FORBIDDEN)

            user = User.objects.get(email=email)
            user.password = make_password(new_password)
            user.save()

            return Response({"status": True, "message": "Password changed successfully"})
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class AddUserWithDevice(APIView):
    """Add user with device assignment - matches Postman collection format"""
    permission_classes = [AllowAny]  # Adjust permissions as needed

    @swagger_auto_schema(
        operation_summary="Add user with device assignment",
        operation_description="Creates a new user and assigns a device in one operation. Matches the Postman collection format.",
        request_body=UserDeviceCreateSerializer,
        responses={
            201: "User created and device assigned successfully",
            400: "Invalid input"
        }
    )
    def post(self, request):
        serializer = UserDeviceCreateSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            return Response(
                {
                    "status": True,
                    "message": "User created and device assigned successfully",
                    "data": serializer.data
                },
                status=status.HTTP_201_CREATED
            )
        return Response(
            {"status": False, "message": "Validation failed", "errors": serializer.errors},
            status=status.HTTP_400_BAD_REQUEST
        )


class UpdateUserDeviceOnly(APIView):
    """Update user device ID only - matches Postman collection format"""
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_summary="Update user device ID only",
        operation_description="Updates only the device assignment for a user. Matches the Postman collection format.",
        request_body=UserDeviceUpdateSerializer,
        responses={
            200: "User device updated successfully",
            404: "User not found",
            400: "Invalid input"
        }
    )
    def put(self, request, id):
        try:
            user = User.objects.get(id=id)
        except ObjectDoesNotExist:
            return Response(
                {"status": False, "message": "User not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check permissions - admin/manager can update any user, users can update themselves
        if request.user.id != user.id and (not request.user.role or request.user.role.name.lower() not in ['admin', 'manager']):
            return Response(
                {"status": False, "message": "You don't have permission to update this user"},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = UserDeviceUpdateSerializer(user, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {
                    "status": True,
                    "message": "User device updated successfully",
                    "data": serializer.data
                }
            )
        return Response(
            {"status": False, "message": "Validation failed", "errors": serializer.errors},
            status=status.HTTP_400_BAD_REQUEST
        )


class RemoveUserDevice(APIView):
    """Remove device assignment from user"""
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_summary="Remove device assignment from user",
        operation_description="Removes device assignment from a user. Admin/manager can remove from any user, users can remove from themselves.",
        responses={
            200: "Device assignment removed successfully",
            404: "User not found",
            403: "Permission denied"
        }
    )
    def delete(self, request, id):
        try:
            user = User.objects.get(id=id)
        except ObjectDoesNotExist:
            return Response(
                {"status": False, "message": "User not found"},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check permissions - admin/manager can update any user, users can update themselves
        if request.user.id != user.id and (not request.user.role or request.user.role.name.lower() not in ['admin', 'manager']):
            return Response(
                {"status": False, "message": "You don't have permission to update this user"},
                status=status.HTTP_403_FORBIDDEN
            )

        try:
            from lips.models import DeviceMaster, UserMaster
            user_master = UserMaster.objects.get(id=user.username)

            # Remove device assignment
            DeviceMaster.objects.filter(user_id=user_master).update(user_id=None)

            return Response(
                {
                    "status": True,
                    "message": "Device assignment removed successfully",
                    "data": {
                        "id": user.id,
                        "username": user.username,
                        "email": user.email,
                        "full_name": user.full_name,
                        "device_id": None,
                        "status": user.status
                    }
                }
            )
        except UserMaster.DoesNotExist:
            return Response(
                {
                    "status": True,
                    "message": "No device assignment found for this user",
                    "data": {
                        "id": user.id,
                        "username": user.username,
                        "email": user.email,
                        "full_name": user.full_name,
                        "device_id": None,
                        "status": user.status
                    }
                }
            )

