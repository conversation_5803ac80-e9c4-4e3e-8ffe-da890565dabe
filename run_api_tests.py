#!/usr/bin/env python
"""
API Test Runner
Starts Django server and runs comprehensive API tests
"""

import os
import sys
import subprocess
import time
import signal
import requests
from threading import Thread

def check_server_ready(url="http://localhost:8000", timeout=30):
    """Check if Django server is ready"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            response = requests.get(url)
            if response.status_code in [200, 404]:  # 404 is OK, means server is running
                return True
        except requests.exceptions.RequestException:
            pass
        time.sleep(1)
    return False

def run_django_server():
    """Start Django development server"""
    print("🚀 Starting Django development server...")
    
    # Change to the project directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # Start Django server
    try:
        process = subprocess.Popen(
            [sys.executable, 'manage.py', 'runserver', '8000'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            preexec_fn=os.setsid if hasattr(os, 'setsid') else None
        )
        
        # Wait for server to be ready
        print("⏳ Waiting for server to start...")
        if check_server_ready():
            print("✅ Django server is ready!")
            return process
        else:
            print("❌ Server failed to start within timeout")
            process.terminate()
            return None
            
    except Exception as e:
        print(f"❌ Failed to start Django server: {e}")
        return None

def run_tests():
    """Run the API tests"""
    print("\n🧪 Running API tests...")
    try:
        # Run the test script
        result = subprocess.run([sys.executable, 'test_all_apis.py'], 
                              capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
            
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Failed to run tests: {e}")
        return False

def main():
    """Main function"""
    print("🔧 API Test Suite Runner")
    print("=" * 50)
    
    # Start Django server
    server_process = run_django_server()
    if not server_process:
        print("❌ Cannot proceed without Django server")
        return 1
    
    try:
        # Run tests
        success = run_tests()
        
        if success:
            print("\n✅ All tests completed successfully!")
            return 0
        else:
            print("\n❌ Some tests failed!")
            return 1
            
    finally:
        # Clean up: stop Django server
        print("\n🛑 Stopping Django server...")
        try:
            if hasattr(os, 'killpg'):
                os.killpg(os.getpgid(server_process.pid), signal.SIGTERM)
            else:
                server_process.terminate()
            server_process.wait(timeout=5)
        except:
            if hasattr(os, 'killpg'):
                os.killpg(os.getpgid(server_process.pid), signal.SIGKILL)
            else:
                server_process.kill()
        print("✅ Django server stopped")

if __name__ == "__main__":
    sys.exit(main())
