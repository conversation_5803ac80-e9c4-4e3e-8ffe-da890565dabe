from rest_framework import serializers
from django.utils import timezone
from .models import (
    DeviceMaster,
    WorkInfo,
    SettingsInfo,
    PermitedApproachInfo,
    ActiveWork,
    HistoricalJudgeAlertInstruction
)


class DeviceMasterSerializer(serializers.ModelSerializer):
    assignedWork = serializers.CharField(source='work_id.work_name', read_only=True)
    usageTime = serializers.SerializerMethodField()
    approachAreaDistance = serializers.FloatField(source='approach_area_distance', required=False)
    approachAreaSeconds = serializers.IntegerField(source='approach_area_seconds', required=False)

    class Meta:
        model = DeviceMaster
        fields = '__all__'
        read_only_fields = ['id']  # ID is auto-generated by Django


    def get_usageTime(self, obj):
        # If work_time is not set, return "-"
        if not obj.work_time:
            return "-"
        return obj.work_time


class DeviceCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating new devices"""
    name = serializers.CharField(source='device_name', required=True)
    deviceId = serializers.CharField(source='display_device_id', required=True)
    charge = serializers.IntegerField(source='battery', required=False)
    status = serializers.CharField(required=False, default='active')

    class Meta:
        model = DeviceMaster
        fields = ['name', 'deviceId', 'charge', 'status']

    def create(self, validated_data):
        return DeviceMaster.objects.create(**validated_data)


class DeviceUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating devices"""
    name = serializers.CharField(source='device_name', required=False)
    charge = serializers.IntegerField(source='battery', required=False)
    status = serializers.CharField(required=False)

    class Meta:
        model = DeviceMaster
        fields = ['name', 'charge', 'status']

    def update(self, instance, validated_data):
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance


class DeviceSettingsSerializer(serializers.ModelSerializer):
    """Serializer for device settings"""
    name = serializers.CharField(source='device_name')
    deviceId = serializers.CharField(source='display_device_id')
    userId = serializers.CharField(source='user_id.id', read_only=True, allow_null=True)
    workId = serializers.IntegerField(source='work_id.id', read_only=True, allow_null=True)
    workName = serializers.CharField(source='work_id.work_name', read_only=True, allow_null=True)
    workTime = serializers.CharField(source='work_time', read_only=True, allow_null=True)
    battery = serializers.IntegerField(read_only=True, allow_null=True)
    signalPeriod = serializers.IntegerField(source='signal_period', allow_null=True)
    approachDistance = serializers.SerializerMethodField()
    approachSeconds = serializers.SerializerMethodField()
    approachAreaDistance = serializers.FloatField(source='approach_area_distance', allow_null=True)
    approachAreaSeconds = serializers.IntegerField(source='approach_area_seconds', allow_null=True)
    status = serializers.SerializerMethodField()
    createdAt = serializers.CharField(source='created_at', read_only=True, allow_null=True)
    updatedAt = serializers.CharField(source='updated_at', read_only=True, allow_null=True)

    class Meta:
        model = DeviceMaster
        fields = [
            'id', 'name', 'deviceId', 'userId', 'workId', 'workName', 'workTime',
            'battery', 'signalPeriod', 'approachDistance', 'approachSeconds',
            'approachAreaDistance', 'approachAreaSeconds', 'status', 'createdAt', 'updatedAt'
        ]
        read_only_fields = ['id']  # ID is auto-generated by Django

    def get_approachDistance(self, obj):
        # Get approach distance from settings
        if obj.user_id:
            # Try to find setting with either user_id or user_id_neighborhoodThreshold
            settings = SettingsInfo.objects.filter(
                key_name='neighborhoodThreshold'
            ).filter(
                id__in=[obj.user_id.id, f"{obj.user_id.id}_neighborhoodThreshold"]
            ).first()

            if settings:
                return float(settings.value) / 1000  # Convert mm to meters
        return 5.0  # Default value

    def get_approachSeconds(self, obj):
        # Get approach seconds from settings
        if obj.user_id:
            # Try to find setting with either user_id or user_id_estimationSec
            settings = SettingsInfo.objects.filter(
                key_name='estimationSec'
            ).filter(
                id__in=[obj.user_id.id, f"{obj.user_id.id}_estimationSec"]
            ).first()

            if settings:
                return int(settings.value)
        # If no setting found, use signal_period or default
        return obj.signal_period or 30

    def get_status(self, obj):
        # Return the actual status from the device, or "active" as default
        return obj.status or "active"


class UpdateDeviceSettingsSerializer(serializers.Serializer):
    """Serializer for updating device settings"""
    name = serializers.CharField(required=False, help_text="Device name")
    deviceId = serializers.CharField(required=False, help_text="Device ID")
    signalPeriod = serializers.IntegerField(required=False, help_text="Signal period in seconds")
    approachDistance = serializers.FloatField(required=True, help_text="Approach distance in meters")
    approachSeconds = serializers.IntegerField(required=True, help_text="Approach time in seconds")
    approachAreaDistance = serializers.FloatField(required=False, help_text="Approach area distance in meters")
    approachAreaSeconds = serializers.IntegerField(required=False, help_text="Approach area time in seconds")

    # Additional fields for complete device update
    # Note: userId is not included as it comes from the authenticated user
    workId = serializers.IntegerField(required=False, help_text="Work ID")
    workTime = serializers.CharField(required=False, help_text="Work time")
    battery = serializers.IntegerField(required=False, help_text="Battery level")
    previousAlertInstruction = serializers.CharField(required=False, help_text="Previous alert instruction")
    status = serializers.CharField(required=False, help_text="Device status")


class WorkInfoSerializer(serializers.ModelSerializer):
    """Serializer for WorkInfo model"""
    work_name = serializers.CharField(required=True, help_text="Name of the work")
    group_num = serializers.CharField(required=True, help_text="Group number")

    class Meta:
        model = WorkInfo
        fields = ['id', 'work_name', 'group_num']
        read_only_fields = ['id']  # ID is auto-generated by Django


class DeviceStatusSerializer(serializers.Serializer):
    """Serializer for device status request"""
    deviceId = serializers.CharField(required=True, help_text="Device ID (display_device_id)")
    workId = serializers.IntegerField(required=True, help_text="Work ID")


class WorkDetailSerializer(serializers.ModelSerializer):
    """Serializer for detailed WorkInfo model with all fields"""
    user = serializers.SerializerMethodField()
    created_at = serializers.CharField(read_only=True)
    updated_at = serializers.CharField(read_only=True)
    assigned_devices_count = serializers.SerializerMethodField()

    class Meta:
        model = WorkInfo
        fields = ['id', 'work_name', 'group_num', 'user', 'created_at', 'updated_at', 'assigned_devices_count']

    def get_user(self, obj):
        if obj.user_id:
            return {
                'id': obj.user_id.id,
                'created_at': obj.user_id.created_at,
                'updated_at': obj.user_id.updated_at
            }
        return None

    def get_assigned_devices_count(self, obj):
        return DeviceMaster.objects.filter(work_id=obj.id).count()


class WorkGroupSerializer(serializers.ModelSerializer):
    """Serializer for work groups"""
    workName = serializers.CharField(source='work_name')
    groupNumber = serializers.CharField(source='group_num')
    assignedDevices = serializers.SerializerMethodField()
    timeElapsed = serializers.SerializerMethodField()
    accessibleAreas = serializers.SerializerMethodField()

    class Meta:
        model = WorkInfo
        fields = ['id', 'workName', 'groupNumber', 'assignedDevices', 'timeElapsed', 'accessibleAreas']

    def get_assignedDevices(self, obj):
        # Count devices assigned to this work
        return DeviceMaster.objects.filter(work_id=obj.id).count()

    def get_timeElapsed(self, obj):
        # For demo purposes, return a fixed time
        return "07:48:59.037"

    def get_accessibleAreas(self, obj):
        # Count accessible areas for this work
        return PermitedApproachInfo.objects.filter(work_id=obj.id).count()


class ActiveWorkSerializer(serializers.ModelSerializer):
    """Serializer for ActiveWork model"""
    class Meta:
        model = ActiveWork
        fields = ['id', 'work_name', 'group_number', 'assigned_devices', 'time_elapsed', 'accessible_areas', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']

    def create(self, validated_data):
        # Custom create logic if needed, e.g., handling multiple objects if request is a list
        # For now, assume single object creation or that DRF's default list handling is sufficient
        return ActiveWork.objects.create(**validated_data)

    def update(self, instance, validated_data):
        # Custom update logic
        instance.work_name = validated_data.get('work_name', instance.work_name)
        instance.group_number = validated_data.get('group_number', instance.group_number)
        instance.assigned_devices = validated_data.get('assigned_devices', instance.assigned_devices)
        instance.time_elapsed = validated_data.get('time_elapsed', instance.time_elapsed)
        instance.accessible_areas = validated_data.get('accessible_areas', instance.accessible_areas)
        instance.save()
        return instance


class DeviceTimerResetSerializer(serializers.Serializer):
    """Serializer for device timer reset"""
    work_id = serializers.IntegerField(required=True)


class WorkTimeCalculationSerializer(serializers.Serializer):
    """Serializer for work time calculation response"""
    id = serializers.IntegerField()
    work_name = serializers.CharField()
    group_number = serializers.CharField()
    created_at = serializers.DateTimeField()
    last_reset_time = serializers.DateTimeField(allow_null=True)
    current_time = serializers.DateTimeField()
    elapsed_time_since_creation = serializers.CharField()
    elapsed_time_since_reset = serializers.CharField(allow_null=True)
    total_calculated_time = serializers.CharField()
    assigned_devices = serializers.IntegerField()
    accessible_areas = serializers.IntegerField()
    status = serializers.CharField()


class DeviceAssignmentSerializer(serializers.Serializer):
    """Serializer for device work assignment"""
    assignedWork = serializers.CharField(required=True)


class AlertSerializer(serializers.ModelSerializer):
    """Serializer for Alert (HistoricalJudgeAlertInstruction) model"""
    device = serializers.IntegerField(source='device_id.id', read_only=True)
    alert_type = serializers.SerializerMethodField()
    message = serializers.SerializerMethodField()
    is_read = serializers.SerializerMethodField()
    created_at = serializers.CharField(read_only=True)

    class Meta:
        model = HistoricalJudgeAlertInstruction
        fields = ['id', 'device', 'alert_type', 'message', 'is_read', 'created_at']
        read_only_fields = ['id', 'created_at']

    def get_alert_type(self, obj):
        """Get alert type based on alert_instruction"""
        if obj.alert_instruction == 1:
            return "approach"
        elif obj.alert_instruction == 2:
            return "entry"
        elif obj.alert_instruction == 3:
            return "battery_low"
        else:
            return "general"

    def get_message(self, obj):
        """Generate message based on alert type"""
        alert_type = self.get_alert_type(obj)
        device_name = obj.device_id.device_name if obj.device_id else "Unknown Device"

        if alert_type == "approach":
            return f"Device {device_name} is approaching a prohibited area"
        elif alert_type == "entry":
            return f"Device {device_name} has entered a prohibited area"
        elif alert_type == "battery_low":
            return f"Device {device_name} has low battery"
        else:
            return f"Alert from device {device_name}"

    def get_is_read(self, obj):
        """For now, return False as there's no is_read field in the model"""
        return False


class AlertCreateSerializer(serializers.Serializer):
    """Serializer for creating alerts"""
    device = serializers.IntegerField(required=True, help_text="Device ID")
    alert_type = serializers.ChoiceField(
        choices=['approach', 'entry', 'battery_low', 'general'],
        required=True,
        help_text="Type of alert"
    )
    message = serializers.CharField(required=True, help_text="Alert message")
    is_read = serializers.BooleanField(default=False, help_text="Whether alert is read")


class AlertUpdateSerializer(serializers.Serializer):
    """Serializer for updating alerts"""
    device = serializers.IntegerField(required=False, help_text="Device ID")
    alert_type = serializers.ChoiceField(
        choices=['approach', 'entry', 'battery_low', 'general'],
        required=False,
        help_text="Type of alert"
    )
    message = serializers.CharField(required=False, help_text="Alert message")
    is_read = serializers.BooleanField(required=False, help_text="Whether alert is read")


class WorkApprovalSerializer(serializers.Serializer):
    """Serializer for work approval/rejection"""
    device_id = serializers.IntegerField(help_text="Device ID to approve/reject work for")
    status = serializers.ChoiceField(
        choices=['approved', 'rejected'],
        help_text="Approval status: 'approved' or 'rejected'"
    )

    def validate_device_id(self, value):
        """Validate that device exists"""
        try:
            device = DeviceMaster.objects.get(id=value)
            return value
        except DeviceMaster.DoesNotExist:
            raise serializers.ValidationError(f"Device with ID '{value}' not found")

    def save(self):
        """Process the approval/rejection"""
        device_id = self.validated_data['device_id']
        approval_status = self.validated_data['status']

        device = DeviceMaster.objects.get(id=device_id)
        device.work_approval_status = approval_status

        if approval_status == 'approved':
            device.approval_message = "Congratulations your work approved"
        else:
            device.approval_message = "Sorry Your work rejected"

        device.approved_at = timezone.now()
        device.save()

        return {
            'device_id': device_id,
            'status': approval_status,
            'message': device.approval_message,
            'device_name': device.device_name,
            'work_name': device.work_id.work_name if device.work_id else None
        }
