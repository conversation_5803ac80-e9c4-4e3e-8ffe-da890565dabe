from django.contrib import admin
from .models import (
    AlertInstructionManagementInfo,
    DeviceMaster,
    DeviceReceiveData,
    FixInfo,
    HistoricalJudgeAlertInstruction,
    PermitedApproachInfo,
    ProhibitedApproachInfo,
    SettingsInfo,
    UserMaster,
    WorkInfo
)

@admin.register(AlertInstructionManagementInfo)
class AlertInstructionManagementInfoAdmin(admin.ModelAdmin):
    list_display = ('id',)

@admin.register(DeviceMaster)
class DeviceMasterAdmin(admin.ModelAdmin):
    list_display = ('id', 'user_id', 'device_name', 'battery', 'created_at')
    search_fields = ('id', 'user_id', 'device_name')
    list_filter = ('user_id',)

@admin.register(DeviceReceiveData)
class DeviceReceiveDataAdmin(admin.ModelAdmin):
    list_display = ('id', 'device_id', 'time', 'battery', 'created_at')
    search_fields = ('id', 'device_id')
    list_filter = ('device_id',)

@admin.register(FixInfo)
class FixInfoAdmin(admin.ModelAdmin):
    list_display = ('id', 'key_name', 'val', 'summary')
    search_fields = ('id', 'key_name')
    list_filter = ('key_name',)

@admin.register(HistoricalJudgeAlertInstruction)
class HistoricalJudgeAlertInstructionAdmin(admin.ModelAdmin):
    list_display = ('id', 'device_id', 'alert_instruction', 'created_at')
    search_fields = ('id', 'device_id', 'alert_id')
    list_filter = ('device_id', 'alert_instruction')

@admin.register(PermitedApproachInfo)
class PermitedApproachInfoAdmin(admin.ModelAdmin):
    list_display = ('id', 'area_element_num', 'work_id', 'created_at')
    search_fields = ('id', 'work_id')
    list_filter = ('work_id',)

@admin.register(ProhibitedApproachInfo)
class ProhibitedApproachInfoAdmin(admin.ModelAdmin):
    list_display = ('id', 'latitude', 'longitude', 'prefectures', 'municipalities')
    search_fields = ('id', 'prefectures', 'municipalities')
    list_filter = ('prefectures',)

@admin.register(SettingsInfo)
class SettingsInfoAdmin(admin.ModelAdmin):
    list_display = ('id', 'key_name', 'value', 'summary')
    search_fields = ('id', 'key_name')
    list_filter = ('key_name',)

@admin.register(UserMaster)
class UserMasterAdmin(admin.ModelAdmin):
    list_display = ('id', 'created_at', 'updated_at')
    search_fields = ('id',)

@admin.register(WorkInfo)
class WorkInfoAdmin(admin.ModelAdmin):
    list_display = ('id', 'user_id', 'work_name', 'group_num', 'created_at')
    search_fields = ('id', 'user_id', 'work_name')
    list_filter = ('user_id',)
