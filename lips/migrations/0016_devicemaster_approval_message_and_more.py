# Generated by Django 4.2.20 on 2025-06-18 02:14

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lips", "0015_alter_devicemaster_updated_at"),
    ]

    operations = [
        migrations.AddField(
            model_name="devicemaster",
            name="approval_message",
            field=models.TextField(
                blank=True, help_text="Approval/rejection message", null=True
            ),
        ),
        migrations.AddField(
            model_name="devicemaster",
            name="approved_at",
            field=models.DateTimeField(
                blank=True,
                help_text="Timestamp when work was approved/rejected",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="devicemaster",
            name="work_approval_status",
            field=models.CharField(
                choices=[
                    ("pending", "Pending"),
                    ("approved", "Approved"),
                    ("rejected", "Rejected"),
                ],
                default="pending",
                help_text="Work approval status",
                max_length=20,
            ),
        ),
    ]
