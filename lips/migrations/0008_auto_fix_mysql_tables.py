from django.db import migrations

class Migration(migrations.Migration):
    dependencies = [
        ('lips', '0007_alter_devicemaster_id_alter_workinfo_id'),
    ]

    operations = [
        # Modify the lips_work_info table to ensure ID is auto-incrementing
        migrations.RunSQL(
            """
            ALTER TABLE lips_work_info MODIFY COLUMN id INT AUTO_INCREMENT;
            """,
            reverse_sql="""
            -- No reverse SQL needed
            """
        ),
        
        # Modify the lips_device_masters table to ensure ID is auto-incrementing
        migrations.RunSQL(
            """
            ALTER TABLE lips_device_masters MODIFY COLUMN id INT AUTO_INCREMENT;
            """,
            reverse_sql="""
            -- No reverse SQL needed
            """
        ),
    ]
