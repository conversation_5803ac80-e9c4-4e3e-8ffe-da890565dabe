from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase, APIClient
from usermanagement.models import User, Role
import unittest
import uuid
from .models import (
    DeviceMaster,
    UserMaster,
    WorkInfo,
    SettingsInfo
)


class LipsAPITestCase(APITestCase):
    """Test case for Lips API"""

    def setUp(self):
        """Set up test data"""
        # Create admin role if it doesn't exist
        admin_role, _ = Role.objects.get_or_create(name='admin')

        # Clean up existing users that might conflict
        User.objects.filter(username='testuser').delete()
        User.objects.filter(email='<EMAIL>').delete() # Make sure email is free

        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        self.user.role = admin_role
        self.user.save()

        # Generate unique IDs for test data
        import uuid
        user_master_id = f'test-user-{uuid.uuid4()}' # For UserMaster and SettingsInfo

        # Create test user master
        self.user_master = UserMaster.objects.create(
            id=user_master_id
            # Assuming UserMaster model handles created_at/updated_at automatically
            # or they are not critical for this specific test's direct query.
            # If specific historical timestamps are needed and UserMaster uses auto_now/auto_now_add:
            # self.user_master.created_at = '2025-04-01 10:00:00'
            # self.user_master.updated_at = '2025-04-01 10:00:00'
            # self.user_master.save()
        )

        # Create test work info (ID will be auto-generated)
        self.work_info = WorkInfo.objects.create(
            user_id=self.user_master,
            work_name='SVLR0008',
            group_num='1111'
            # created_at and updated_at are auto_now_add and auto_now respectively in the model
        )
        self.assertIsNotNone(self.work_info.id, "WorkInfo ID should not be None after creation.")

        # Create test device master (ID will be auto-generated)
        self.device_master = DeviceMaster.objects.create(
            user_id=self.user_master,
            display_device_id='0010',
            device_name='motomachi',
            work_id=self.work_info, # Assign the WorkInfo instance
            battery=59,
            signal_period=360
            # created_at is auto_now_add, updated_at is auto_now in the model
        )
        self.assertIsNotNone(self.device_master.id, "DeviceMaster ID should not be None after creation.")
        self.assertIsNotNone(self.device_master.created_at, "DeviceMaster created_at should be set upon creation.") # Verify created_at as well
        self.assertIsNotNone(self.device_master.updated_at, "DeviceMaster updated_at should be set upon creation.")

        # Verify the DeviceMaster creation and linkage in setUp
        retrieved_device_in_setup = DeviceMaster.objects.get(id=self.device_master.id)
        self.assertEqual(retrieved_device_in_setup.display_device_id, '0010', "Mismatch in display_device_id after creation.")
        self.assertIsNotNone(retrieved_device_in_setup.work_id, "DeviceMaster's work_id should not be None after creation.")
        self.assertEqual(retrieved_device_in_setup.work_id.id, self.work_info.id, "DeviceMaster's work_id does not match WorkInfo's id after creation.")
        self.assertIsNotNone(retrieved_device_in_setup.created_at, "DeviceMaster created_at (from DB in setUp) should be set upon creation.")
        self.assertIsNotNone(retrieved_device_in_setup.updated_at, "DeviceMaster updated_at (from DB in setUp) should be set upon creation.") # Added this line


        # Create test settings
        self.settings_neighborhood = SettingsInfo.objects.create(
            id=user_master_id, # Uses the same UUID as UserMaster for this test setup
            key_name='neighborhoodThreshold',
            value='1000',
            summary='接近検知距離の閾値 [mm]'
            # Assuming SettingsInfo also has auto_now_add/auto_now for timestamps
            # If specific historical timestamps are needed:
            # self.settings_neighborhood.created_at = '2025-04-01 10:00:00'
            # self.settings_neighborhood.updated_at = '2025-04-01 10:00:00'
            # self.settings_neighborhood.save()
        )

        # Set up client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)

    def test_get_all_devices(self):
        """Test getting all devices"""
        url = reverse('device-list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(len(response.data['data']['devices']), 1)
        self.assertEqual(response.data['data']['devices'][0]['name'], 'motomachi')

    def test_get_device_by_id(self):
        """Test getting a device by ID"""
        url = reverse('device-detail', args=[self.device_master.id])
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['name'], 'motomachi')

    def test_assign_device(self):
        """Test assigning a work to a device"""
        url = reverse('device-assign', args=[self.device_master.id])
        data = {'assignedWork': 'SVLR0008'}
        response = self.client.put(url, data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['assignedWork'], 'SVLR0008')

    # Skipping device settings tests for now
    @unittest.skip("Device settings API needs to be fixed")
    def test_get_device_settings(self):
        """Test getting device settings"""
        url = reverse('device-settings-list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(len(response.data['data']['deviceSettings']), 1)
        self.assertEqual(response.data['data']['deviceSettings'][0]['approachDistance'], 1.0)
        self.assertEqual(response.data['data']['deviceSettings'][0]['approachSeconds'], 3)

    @unittest.skip("Device settings API needs to be fixed")
    def test_update_device_settings(self):
        """Test updating device settings"""
        url = reverse('device-settings-detail', args=[self.device_master.id])
        data = {
            'approachDistance': 5.5,
            'approachSeconds': 5
        }
        response = self.client.put(url, data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['approachDistance'], 5.5)
        self.assertEqual(response.data['data']['approachSeconds'], 5)

        # Check if settings were updated in the database
        settings_neighborhood = SettingsInfo.objects.get(id=self.user_master.id, key_name='neighborhoodThreshold')

        self.assertEqual(settings_neighborhood.value, '5500')

    def test_get_all_works(self):
        """Test getting all works"""
        url = reverse('works-list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        
        # Extract work names from the list of work dictionaries
        work_names = [work['work_name'] for work in response.data['data']['works']]
        self.assertIn('SVLR0008', work_names)

    def test_get_work_details(self):
        """Test getting work details"""
        url = reverse('work-detail', args=[self.work_info.id])
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['workName'], 'SVLR0008')
        self.assertEqual(response.data['data']['groupNumber'], '1111')
        self.assertEqual(response.data['data']['assignedDevices'], 1)

    def test_get_work_details_not_found(self):
        """Test getting work details for non-existent work"""
        url = reverse('work-detail', args=['non-existent-work'])
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertFalse(response.data['success'])
        self.assertIn("not found", response.data['message'])

    def test_create_work(self):
        """Test creating a new work"""
        url = reverse('works-list')
        data = {
            'work_name': 'SVLR0009',
            'group_num': '2222'
        }
        response = self.client.post(url, data)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['work_name'], 'SVLR0009')
        self.assertEqual(response.data['data']['group_num'], '2222')

        # Verify the work was created in the database
        self.assertTrue(WorkInfo.objects.filter(work_name='SVLR0009').exists())

        # Verify that an ID was automatically generated
        created_work = WorkInfo.objects.get(work_name='SVLR0009')
        self.assertIsNotNone(created_work.id)
        self.assertTrue(len(created_work.id) > 0)

        # Verify the ID is returned in the response
        self.assertIsNotNone(response.data['data']['id'])
        self.assertEqual(response.data['data']['id'], created_work.id)

    def test_create_work_with_custom_id(self):
        """Test creating a work with a custom ID"""
        url = reverse('works-list')
        custom_id = f'custom-id-{uuid.uuid4()}'
        data = {
            'id': custom_id,
            'work_name': 'SVLR0011',
            'group_num': '3333'
        }
        response = self.client.post(url, data)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['id'], custom_id)
        self.assertEqual(response.data['data']['work_name'], 'SVLR0011')
        self.assertEqual(response.data['data']['group_num'], '3333')

        # Verify the work was created in the database with the custom ID
        self.assertTrue(WorkInfo.objects.filter(id=custom_id).exists())

    def test_create_work_invalid_data(self):
        """Test creating a work with invalid data"""
        url = reverse('works-list')
        data = {
            # Missing required field 'work_name'
            'group_num': '2222'
        }
        response = self.client.post(url, data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])

    def test_delete_work(self):
        """Test deleting a work"""
        # Create a work without assigned devices
        delete_work_id = f'test-work-to-delete-{uuid.uuid4()}'
        delete_work = WorkInfo.objects.create(
            id=delete_work_id,
            user_id=self.user_master,
            work_name='SVLR0010',
            group_num='3333',
            created_at='2025-04-01 10:00:00',
            updated_at='2025-04-01 10:00:00'
        )

        url = reverse('work-detail', args=[delete_work_id])
        response = self.client.delete(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn("削除されました", response.data['message'])  # "has been deleted" in Japanese

        # Verify the work was deleted from the database
        self.assertFalse(WorkInfo.objects.filter(id=delete_work_id).exists())

    def test_delete_work_with_assigned_devices(self):
        """Test deleting a work with assigned devices"""
        # The work_info already has a device assigned to it
        url = reverse('work-detail', args=[self.work_info.id])
        response = self.client.delete(url)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn("assigned devices", response.data['message'])

    def test_delete_work_not_found(self):
        """Test deleting a non-existent work"""
        url = reverse('work-detail', args=['non-existent-work'])
        response = self.client.delete(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertFalse(response.data['success'])
        self.assertIn("not found", response.data['message'])

    def test_get_device_status_success(self):
        """Test getting device status successfully"""
        url = reverse('device-status')
        data = {
            'deviceId': self.device_master.display_device_id,
            'workId': self.work_info.id
        }
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('data', response.data)
        self.assertIsInstance(response.data['data'], dict)
        self.assertIn('message', response.data)
        self.assertEqual(response.data['message'], 'Device data retrieved successfully')

        returned_data = response.data['data']
        # Verify all expected fields are present
        self.assertEqual(returned_data['id'], self.device_master.id)
        self.assertEqual(returned_data['display_device_id'], self.device_master.display_device_id)
        self.assertEqual(returned_data['device_name'], self.device_master.device_name)
        self.assertEqual(returned_data['assignedWork'], self.work_info.work_name)
        self.assertEqual(returned_data['battery'], self.device_master.battery)
        self.assertEqual(returned_data['signal_period'], self.device_master.signal_period)
        
        # Check for timestamps
        self.assertIn('created_at', returned_data)
        self.assertIn('updated_at', returned_data)
        self.assertIsNotNone(returned_data['created_at'])
        self.assertIsNotNone(returned_data['updated_at'])

    def test_get_device_status_device_not_found(self):
        """Test getting device status for non-existent device"""
        url = reverse('device-status')
        data = {
            'deviceId': 'non-existent-device-id',
            'workId': self.work_info.id
        }
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertFalse(response.data['success'])
        self.assertIn("not found", response.data['message'])
        self.assertIn('non-existent-device-id', response.data['message'])
        self.assertIn(str(self.work_info.id), response.data['message'])

    def test_get_device_status_work_not_found(self):
        """Test getting device status for non-existent work"""
        url = reverse('device-status')
        non_existent_work_id = 99999
        data = {
            'deviceId': self.device_master.display_device_id,
            'workId': non_existent_work_id
        }
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertFalse(response.data['success'])
        self.assertIn("not found", response.data['message'])
        self.assertIn(self.device_master.display_device_id, response.data['message'])
        self.assertIn(str(non_existent_work_id), response.data['message'])

    def test_get_device_status_both_not_found(self):
        """Test getting device status when both device and work don't exist"""
        url = reverse('device-status')
        data = {
            'deviceId': 'non-existent-device',
            'workId': 88888
        }
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertFalse(response.data['success'])
        self.assertIn("not found", response.data['message'])

    def test_get_device_status_missing_device_id(self):
        """Test getting device status without deviceId"""
        url = reverse('device-status')
        data = {
            'workId': self.work_info.id
        }
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('message', response.data)

    def test_get_device_status_missing_work_id(self):
        """Test getting device status without workId"""
        url = reverse('device-status')
        data = {
            'deviceId': self.device_master.display_device_id
        }
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('message', response.data)

    def test_get_device_status_empty_payload(self):
        """Test getting device status with empty payload"""
        url = reverse('device-status')
        data = {}
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])

    def test_get_device_status_invalid_work_id_type(self):
        """Test getting device status with invalid workId type"""
        url = reverse('device-status')
        data = {
            'deviceId': self.device_master.display_device_id,
            'workId': 'invalid-work-id'  # Should be integer
        }
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])

    def test_get_device_status_null_values(self):
        """Test getting device status with null values"""
        url = reverse('device-status')
        data = {
            'deviceId': None,
            'workId': None
        }
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])

    def test_get_device_status_wrong_http_method(self):
        """Test getting device status with wrong HTTP method"""
        url = reverse('device-status')
        data = {
            'deviceId': self.device_master.display_device_id,
            'workId': self.work_info.id
        }
        
        # Test GET method (should not be allowed)
        response = self.client.get(url, data)
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)
        
        # Test PUT method (should not be allowed)
        response = self.client.put(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)
        
        # Test DELETE method (should not be allowed)
        response = self.client.delete(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)

    def test_get_device_status_with_different_device_work_combinations(self):
        """Test device status with multiple device and work combinations"""
        # Create another work
        work2 = WorkInfo.objects.create(
            user_id=self.user_master,
            work_name='SVLR0009',
            group_num='2222'
        )
        
        # Create another device
        device2 = DeviceMaster.objects.create(
            user_id=self.user_master,
            display_device_id='0011',
            device_name='test_device_2',
            work_id=work2,
            battery=75,
            signal_period=300
        )

        url = reverse('device-status')
        
        # Test original device with original work
        data = {
            'deviceId': self.device_master.display_device_id,
            'workId': self.work_info.id
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        
        # Test new device with new work
        data = {
            'deviceId': device2.display_device_id,
            'workId': work2.id
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        
        # Test original device with new work (should fail)
        data = {
            'deviceId': self.device_master.display_device_id,
            'workId': work2.id
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertFalse(response.data['success'])

    def test_get_device_status_serializer_validation(self):
        """Test device status with various input validation scenarios"""
        url = reverse('device-status')
        
        # Test with extra fields (should be ignored)
        data = {
            'deviceId': self.device_master.display_device_id,
            'workId': self.work_info.id,
            'extraField': 'should be ignored',
            'anotherExtra': 123
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        
        # Test with empty strings
        data = {
            'deviceId': '',
            'workId': self.work_info.id
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])

    def test_get_device_status_response_structure(self):
        """Test that device status response has correct structure"""
        url = reverse('device-status')
        data = {
            'deviceId': self.device_master.display_device_id,
            'workId': self.work_info.id
        }
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check top-level structure
        self.assertIn('success', response.data)
        self.assertIn('message', response.data)
        self.assertIn('data', response.data)
        
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['message'], 'Device data retrieved successfully')
        self.assertIsInstance(response.data['data'], dict)
        
        # Check data structure (should match DeviceMasterSerializer)
        device_data = response.data['data']
        expected_fields = [
            'id', 'display_device_id', 'device_name', 'assignedWork',
            'battery', 'signal_period', 'created_at', 'updated_at'
        ]
        
        for field in expected_fields:
            self.assertIn(field, device_data, f"Field '{field}' should be present in response")

    def test_get_device_status_with_unicode_characters(self):
        """Test device status with unicode characters in device/work names"""
        # Create device with unicode characters
        unicode_device = DeviceMaster.objects.create(
            user_id=self.user_master,
            display_device_id='デバイス001',  # Japanese characters
            device_name='測定デバイス',
            work_id=self.work_info,
            battery=80,
            signal_period=240
        )

        url = reverse('device-status')
        data = {
            'deviceId': unicode_device.display_device_id,
            'workId': self.work_info.id
        }
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['display_device_id'], 'デバイス001')
        self.assertEqual(response.data['data']['device_name'], '測定デバイス')

    def test_get_device_status_permission_required(self):
        """Test that device status requires proper permissions"""
        # This test assumes the view has permission_classes = [IsAdminOrManager]
        # Create a client without authentication
        unauth_client = APIClient()
        
        url = reverse('device-status')
        data = {
            'deviceId': self.device_master.display_device_id,
            'workId': self.work_info.id
        }
        response = unauth_client.post(url, data, format='json')
        
        # Should return 401 Unauthorized or 403 Forbidden
        self.assertIn(response.status_code, [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN])

    def test_get_device_status_case_sensitivity(self):
        """Test device status with case-sensitive device IDs"""
        # Create device with mixed case ID
        mixed_case_device = DeviceMaster.objects.create(
            user_id=self.user_master,
            display_device_id='AbC123',
            device_name='case_sensitive_device',
            work_id=self.work_info,
            battery=65,
            signal_period=180
        )

        url = reverse('device-status')
        
        # Test exact match
        data = {
            'deviceId': 'AbC123',
            'workId': self.work_info.id
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        
        # Test different case (should fail)
        data = {
            'deviceId': 'abc123',
            'workId': self.work_info.id
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertFalse(response.data['success'])

    def test_get_device_status_with_extreme_values(self):
        """Test device status with boundary and extreme values"""
        # Create device with extreme values
        extreme_device = DeviceMaster.objects.create(
            user_id=self.user_master,
            display_device_id='EXTREME_DEVICE_ID_' + 'X' * 50,  # Very long ID
            device_name='Device with very long name ' + 'Y' * 100,
            work_id=self.work_info,
            battery=0,  # Minimum battery
            signal_period=999999  # Very large signal period
        )

        url = reverse('device-status')
        data = {
            'deviceId': extreme_device.display_device_id,
            'workId': self.work_info.id
        }
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['battery'], 0)
        self.assertEqual(response.data['data']['signal_period'], 999999)

    def test_get_device_status_with_special_characters(self):
        """Test device status with special characters in device ID"""
        # Create device with special characters
        special_device = DeviceMaster.objects.create(
            user_id=self.user_master,
            display_device_id='DEV-001@#$%',
            device_name='Device with special chars !@#$%^&*()',
            work_id=self.work_info,
            battery=50,
            signal_period=120
        )

        url = reverse('device-status')
        data = {
            'deviceId': 'DEV-001@#$%',
            'workId': self.work_info.id
        }
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['display_device_id'], 'DEV-001@#$%')

    def test_get_device_status_with_malformed_json(self):
        """Test device status with malformed JSON payload"""
        url = reverse('device-status')
        
        # Send malformed JSON
        response = self.client.post(
            url, 
            data='{"deviceId": "test", "workId":}',  # Invalid JSON
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_get_device_status_with_very_large_work_id(self):
        """Test device status with very large work ID values"""
        url = reverse('device-status')
        data = {
            'deviceId': self.device_master.display_device_id,
            'workId': 999999999999999999999  # Very large number
        }
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertFalse(response.data['success'])

    def test_get_device_status_with_negative_work_id(self):
        """Test device status with negative work ID"""
        url = reverse('device-status')
        data = {
            'deviceId': self.device_master.display_device_id,
            'workId': -1
        }
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertFalse(response.data['success'])

    def test_get_device_status_concurrent_requests(self):
        """Test device status with multiple concurrent requests"""
        url = reverse('device-status')
        data = {
            'deviceId': self.device_master.display_device_id,
            'workId': self.work_info.id
        }
        
        # Make multiple requests
        responses = []
        for i in range(5):
            response = self.client.post(url, data, format='json')
            responses.append(response)
        
        # All should succeed
        for response in responses:
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertTrue(response.data['success'])

    def test_get_device_status_with_whitespace_in_device_id(self):
        """Test device status with whitespace in device ID"""
        url = reverse('device-status')
        
        # Test with leading/trailing whitespace
        data = {
            'deviceId': '  ' + self.device_master.display_device_id + '  ',
            'workId': self.work_info.id
        }
        response = self.client.post(url, data, format='json')
        
        # Should fail since device ID doesn't match exactly
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertFalse(response.data['success'])

    def test_get_device_status_with_different_content_types(self):
        """Test device status with different content types"""
        url = reverse('device-status')
        data = {
            'deviceId': self.device_master.display_device_id,
            'workId': self.work_info.id
        }
        
        # Test with form data instead of JSON
        response = self.client.post(url, data)  # Default content type
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])

    def test_get_device_status_response_data_types(self):
        """Test that device status response returns correct data types"""
        url = reverse('device-status')
        data = {
            'deviceId': self.device_master.display_device_id,
            'workId': self.work_info.id
        }
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        device_data = response.data['data']
        
        # Verify data types
        self.assertIsInstance(device_data['id'], int)
        self.assertIsInstance(device_data['display_device_id'], str)
        self.assertIsInstance(device_data['device_name'], str)
        self.assertIsInstance(device_data['battery'], int)
        self.assertIsInstance(device_data['signal_period'], int)
        self.assertIsInstance(device_data['created_at'], str)
        self.assertIsInstance(device_data['updated_at'], str)

    def test_get_device_status_with_none_values_in_device(self):
        """Test device status when device has None/null values"""
        # Create device with minimal data
        minimal_device = DeviceMaster.objects.create(
            user_id=self.user_master,
            display_device_id='MINIMAL_DEV',
            device_name='Minimal Device',
            work_id=self.work_info,
            battery=None,  # Null battery
            signal_period=None,  # Null signal period
            work_time=None,  # Null work time
            previous_alert_instruction=None,  # Null alert instruction
            status=None  # Null status
        )

        url = reverse('device-status')
        data = {
            'deviceId': 'MINIMAL_DEV',
            'workId': self.work_info.id
        }
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        
        # Verify null values are handled properly
        device_data = response.data['data']
        self.assertIsNone(device_data['battery'])
        self.assertIsNone(device_data['signal_period'])

    def test_get_device_status_error_message_format(self):
        """Test that error messages follow consistent format"""
        url = reverse('device-status')
        
        # Test various error scenarios and check message format
        test_cases = [
            {
                'data': {'deviceId': 'nonexistent', 'workId': self.work_info.id},
                'expected_in_message': ['not found', 'nonexistent', str(self.work_info.id)]
            },
            {
                'data': {'deviceId': self.device_master.display_device_id, 'workId': 99999},
                'expected_in_message': ['not found', self.device_master.display_device_id, '99999']
            },
            {
                'data': {'deviceId': '', 'workId': self.work_info.id},
                'expected_in_message': []  # Just check it has a message
            }
        ]
        
        for test_case in test_cases:
            response = self.client.post(url, test_case['data'], format='json')
            self.assertFalse(response.data['success'])
            self.assertIn('message', response.data)
            self.assertIsInstance(response.data['message'], str)
            
            # Check specific message content if expected
            for expected_text in test_case['expected_in_message']:
                self.assertIn(expected_text, response.data['message'])

    def test_get_device_status_serializer_edge_cases(self):
        """Test DeviceStatusSerializer with edge cases"""
        url = reverse('device-status')
        
        # Test with zero work ID
        data = {
            'deviceId': self.device_master.display_device_id,
            'workId': 0
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        
        # Test with very long device ID
        data = {
            'deviceId': 'X' * 1000,  # Very long device ID
            'workId': self.work_info.id
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_get_device_status_database_integrity(self):
        """Test device status maintains database integrity"""
        url = reverse('device-status')
        data = {
            'deviceId': self.device_master.display_device_id,
            'workId': self.work_info.id
        }
        
        # Store original device data
        original_updated_at = self.device_master.updated_at
        
        # Make request
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify device data wasn't modified by read operation
        self.device_master.refresh_from_db()
        self.assertEqual(self.device_master.updated_at, original_updated_at)
