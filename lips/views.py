from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.pagination import PageNumberPagination
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from django.utils import timezone
from datetime import datetime, timedelta

from usermanagement.permissions import IsAdminOrManager, IsAdminOrManagerOrReadOnly

from .models import (
    DeviceMaster,
    WorkInfo,
    SettingsInfo,
    UserMaster,
    PermitedApproachInfo,
    HistoricalJudgeAlertInstruction
)
from .serializers import (
    DeviceMasterSerializer,
    DeviceSettingsSerializer,
    UpdateDeviceSettingsSerializer,
    DeviceStatusSerializer,
    WorkInfoSerializer,
    WorkDetailSerializer,
    WorkGroupSerializer,
    DeviceCreateSerializer,
    DeviceUpdateSerializer,
    DeviceTimerResetSerializer,
    WorkTimeCalculationSerializer,
    AlertSerializer,
    AlertCreateSerializer,
    AlertUpdateSerializer,
    DeviceAssignmentSerializer
)


class DeviceViewSet(viewsets.ModelViewSet):
    """ViewSet for DeviceMaster model with full CRUD operations"""
    queryset = DeviceMaster.objects.all()
    serializer_class = DeviceMasterSerializer
    permission_classes = [IsAdminOrManagerOrReadOnly]
    http_method_names = ['get', 'post', 'put', 'delete']

    def get_serializer_class(self):
        if self.action == 'create':
            return DeviceCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return DeviceUpdateSerializer
        return DeviceMasterSerializer

    def list(self, request):
        """Get all devices"""
        devices = self.get_queryset()
        serializer = self.get_serializer(devices, many=True)
        return Response({
            'success': True,
            'data': {
                'devices': serializer.data
            }
        })

    def create(self, request):
        """Create a new device"""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            # Set default values
            device_data = serializer.validated_data
            device_data['status'] = device_data.get('status', 'active')
            device_data['signal_period'] = device_data.get('signal_period', 1000)
            
            device = serializer.save()
            
            # Return success response matching Postman collection format
            response_data = {
                'id': str(device.id),
                'display_device_id': device.display_device_id,
                'device_name': device.device_name,
                'battery': device.battery,
                'signal_period': device.signal_period,
                'user_id': device.user_id.id if device.user_id else None,
                'work_id': device.work_id.id if device.work_id else None,
                'status': device.status,
                'work_time': device.work_time,
                'created_at': device.created_at.isoformat() if device.created_at else None,
                'updated_at': device.updated_at.isoformat() if device.updated_at else None,
            }
            
            return Response({
                'success': True,
                'data': response_data
            }, status=status.HTTP_201_CREATED)
        
        return Response({
            'success': False,
            'message': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def retrieve(self, request, pk=None):
        """Get device by ID"""
        try:
            device = self.get_object()
            serializer = self.get_serializer(device)
            
            # Format response to match Postman collection
            response_data = {
                'id': str(device.id),
                'display_device_id': device.display_device_id,
                'device_name': device.device_name,
                'battery': device.battery,
                'signal_period': device.signal_period,
                'user_id': device.user_id.id if device.user_id else None,
                'work_id': device.work_id.id if device.work_id else None,
                'status': device.status,
                'work_time': device.work_time,
                'created_at': device.created_at.isoformat() if device.created_at else None,
                'updated_at': device.updated_at.isoformat() if device.updated_at else None,
            }
            
            return Response({
                'success': True,
                'data': response_data
            })
        except DeviceMaster.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Device not found'
            }, status=status.HTTP_404_NOT_FOUND)

    def update(self, request, pk=None):
        """Update device (PUT)"""
        try:
            device = self.get_object()
            serializer = self.get_serializer(device, data=request.data, partial=False)
            
            if serializer.is_valid():
                # Don't allow updating display_device_id (deviceId)
                if 'display_device_id' in serializer.validated_data:
                    del serializer.validated_data['display_device_id']
                
                device = serializer.save()
                
                response_data = {
                    'id': str(device.id),
                    'display_device_id': device.display_device_id,
                    'device_name': device.device_name,
                    'battery': device.battery,
                    'signal_period': device.signal_period,
                    'user_id': device.user_id.id if device.user_id else None,
                    'work_id': device.work_id.id if device.work_id else None,
                    'status': device.status,
                    'updated_at': device.updated_at.isoformat() if device.updated_at else None,
                }
                
                return Response({
                    'success': True,
                    'data': response_data
                })
            
            return Response({
                'success': False,
                'message': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
            
        except DeviceMaster.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Device not found'
            }, status=status.HTTP_404_NOT_FOUND)

    def destroy(self, request, pk=None):
        """Delete device"""
        try:
            device = self.get_object()
            device.delete()
            return Response({
                'success': True,
                'message': 'Device deleted successfully'
            })
        except DeviceMaster.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Device not found'
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['post'], url_path='timer/reset')
    def reset_timer(self, request, pk=None):
        """Reset device timer when work is assigned"""
        try:
            device = self.get_object()
            serializer = DeviceTimerResetSerializer(data=request.data)
            
            if serializer.is_valid():
                work_id = serializer.validated_data['work_id']
                try:
                    work = WorkInfo.objects.get(id=work_id)
                    device.work_id = work
                    device.work_time = "00:00:00"  # Reset to 00:00:00
                    device.save()
                    
                    return Response({
                        'success': True,
                        'message': 'Device timer reset successfully',
                        'data': {
                            'device_id': str(device.id),
                            'work_id': work_id,
                            'work_time': device.work_time,
                            'reset_timestamp': timezone.now().isoformat()
                        }
                    })
                except WorkInfo.DoesNotExist:
                    return Response({
                        'success': False,
                        'message': f'Work with ID {work_id} not found'
                    }, status=status.HTTP_404_NOT_FOUND)
            
            return Response({
                'success': False,
                'message': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
            
        except DeviceMaster.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Device not found'
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['get'], url_path='work-time')
    def get_work_time(self, request, pk=None):
        """Calculate current work time for device"""
        try:
            device = self.get_object()
            
            if not device.work_id:
                return Response({
                    'success': False,
                    'message': 'No work assigned to this device'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Calculate elapsed time since work assignment
            if device.updated_at:
                elapsed_seconds = (timezone.now() - device.updated_at).total_seconds()
                elapsed_time = str(timedelta(seconds=int(elapsed_seconds)))
            else:
                elapsed_time = "00:00:00"
            
            # Parse accumulated time
            accumulated_time = device.work_time or "00:00:00"
            
            # For demo purposes, calculate total time (in real implementation, you'd add the times)
            total_work_time = accumulated_time  # Simplified for demo
            
            return Response({
                'success': True,
                'data': {
                    'device_id': str(device.id),
                    'work_id': device.work_id.id,
                    'accumulated_time': accumulated_time,
                    'elapsed_since_assignment': elapsed_time,
                    'total_work_time': total_work_time,
                    'work_assigned_at': device.updated_at.isoformat() if device.updated_at else None
                }
            })
            
        except DeviceMaster.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Device not found'
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['put'], url_path='assign')
    def assign(self, request, pk=None):
        """Assign or remove a work assignment for a device"""
        device = self.get_object()
        serializer = DeviceAssignmentSerializer(data=request.data)

        if serializer.is_valid():
            work_name = serializer.validated_data['assignedWork']
            work = WorkInfo.objects.filter(work_name=work_name).first()

            if work:
                device.work_id = work
                device.save()

                # Return updated device
                device_serializer = self.get_serializer(device)
                return Response({
                    'success': True,
                    'message': 'デバイスの割り当てが更新されました',
                    'data': device_serializer.data
                })
            else:
                return Response({
                    'success': False,
                    'message': f"Work '{work_name}' not found"
                }, status=status.HTTP_404_NOT_FOUND)

        return Response({
            'success': False,
            'message': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class DeviceSettingsViewSet(viewsets.ViewSet):
    """
    ViewSet for device settings

    This ViewSet provides endpoints to manage device settings including:
    - Listing all device settings
    - Retrieving settings for a specific device
    - Creating new device settings
    - Updating existing device settings
    """
    permission_classes = [IsAdminOrManagerOrReadOnly]

    def get_permissions(self):
        """
        Override get_permissions to use different permission classes for different actions
        """
        if self.action in ['update', 'create']:
            permission_classes = [IsAdminOrManager]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    @swagger_auto_schema(
        operation_summary="Get all device settings",
        operation_description="Retrieves settings for all devices. Requires admin or manager role.",
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'deviceSettings': openapi.Schema(
                                    type=openapi.TYPE_ARRAY,
                                    items=openapi.Schema(
                                        type=openapi.TYPE_OBJECT,
                                        properties={
                                            'id': openapi.Schema(type=openapi.TYPE_STRING, description='Device ID'),
                                            'name': openapi.Schema(type=openapi.TYPE_STRING, description='Device name'),
                                            'deviceId': openapi.Schema(type=openapi.TYPE_STRING, description='Display device ID'),
                                            'workId': openapi.Schema(type=openapi.TYPE_STRING, description='Work ID', nullable=True),
                                            'workName': openapi.Schema(type=openapi.TYPE_STRING, description='Work name', nullable=True),
                                            'workTime': openapi.Schema(type=openapi.TYPE_STRING, description='Work time', nullable=True),
                                            'battery': openapi.Schema(type=openapi.TYPE_INTEGER, description='Battery level', nullable=True),
                                            'signalPeriod': openapi.Schema(type=openapi.TYPE_INTEGER, description='Signal period in seconds', nullable=True),
                                            'approachDistance': openapi.Schema(type=openapi.TYPE_NUMBER, description='Approach distance in meters'),
                                            'approachSeconds': openapi.Schema(type=openapi.TYPE_INTEGER, description='Approach time in seconds'),
                                            'status': openapi.Schema(type=openapi.TYPE_STRING, description='Device status'),
                                            'createdAt': openapi.Schema(type=openapi.TYPE_STRING, description='Creation timestamp', nullable=True),
                                            'updatedAt': openapi.Schema(type=openapi.TYPE_STRING, description='Last update timestamp', nullable=True),
                                        }
                                    )
                                )
                            }
                        )
                    }
                )
            )
        }
    )
    def list(self, request):
        """Get all device settings"""
        devices = DeviceMaster.objects.all()
        serializer = DeviceSettingsSerializer(devices, many=True)
        return Response({
            'success': True,
            'data': {
                'deviceSettings': serializer.data
            }
        })

    @swagger_auto_schema(
        operation_summary="Get device settings by ID",
        operation_description="Retrieves settings for a specific device. Requires admin or manager role.",
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_STRING, description='Device ID'),
                                'name': openapi.Schema(type=openapi.TYPE_STRING, description='Device name'),
                                'deviceId': openapi.Schema(type=openapi.TYPE_STRING, description='Display device ID'),
                                'workId': openapi.Schema(type=openapi.TYPE_STRING, description='Work ID', nullable=True),
                                'workName': openapi.Schema(type=openapi.TYPE_STRING, description='Work name', nullable=True),
                                'workTime': openapi.Schema(type=openapi.TYPE_STRING, description='Work time', nullable=True),
                                'battery': openapi.Schema(type=openapi.TYPE_INTEGER, description='Battery level', nullable=True),
                                'signalPeriod': openapi.Schema(type=openapi.TYPE_INTEGER, description='Signal period in seconds', nullable=True),
                                'approachDistance': openapi.Schema(type=openapi.TYPE_NUMBER, description='Approach distance in meters'),
                                'approachSeconds': openapi.Schema(type=openapi.TYPE_INTEGER, description='Approach time in seconds'),
                                'status': openapi.Schema(type=openapi.TYPE_STRING, description='Device status'),
                                'createdAt': openapi.Schema(type=openapi.TYPE_STRING, description='Creation timestamp', nullable=True),
                                'updatedAt': openapi.Schema(type=openapi.TYPE_STRING, description='Last update timestamp', nullable=True),
                            }
                        )
                    }
                )
            ),
            404: openapi.Response(
                description="Device not found",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            )
        }
    )
    def retrieve(self, request, pk=None):
        """Get settings for a specific device"""
        try:
            device = DeviceMaster.objects.get(pk=pk)
            serializer = DeviceSettingsSerializer(device)
            return Response({
                'success': True,
                'data': serializer.data
            })
        except DeviceMaster.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Device not found'
            }, status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        operation_summary="Create device settings",
        operation_description="Creates settings for a device. Requires admin or manager role. The ID field is auto-generated by Django and should NOT be included in the request.",
        request_body=UpdateDeviceSettingsSerializer,
        responses={
            201: openapi.Response(
                description="Settings created successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Success message'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER, description='Auto-generated Device ID (integer)'),
                                'name': openapi.Schema(type=openapi.TYPE_STRING, description='Device name'),
                                'deviceId': openapi.Schema(type=openapi.TYPE_STRING, description='Display device ID'),
                                'workId': openapi.Schema(type=openapi.TYPE_INTEGER, description='Work ID (integer)', nullable=True),
                                'workName': openapi.Schema(type=openapi.TYPE_STRING, description='Work name', nullable=True),
                                'workTime': openapi.Schema(type=openapi.TYPE_STRING, description='Work time', nullable=True),
                                'battery': openapi.Schema(type=openapi.TYPE_INTEGER, description='Battery level', nullable=True),
                                'signalPeriod': openapi.Schema(type=openapi.TYPE_INTEGER, description='Signal period in seconds', nullable=True),
                                'approachDistance': openapi.Schema(type=openapi.TYPE_NUMBER, description='Approach distance in meters'),
                                'approachSeconds': openapi.Schema(type=openapi.TYPE_INTEGER, description='Approach time in seconds'),
                                'status': openapi.Schema(type=openapi.TYPE_STRING, description='Device status'),
                                'createdAt': openapi.Schema(type=openapi.TYPE_STRING, description='Creation timestamp', nullable=True),
                                'updatedAt': openapi.Schema(type=openapi.TYPE_STRING, description='Last update timestamp', nullable=True),
                            }
                        )
                    }
                )
            ),
            400: openapi.Response(
                description="Invalid input",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            ),
            404: openapi.Response(
                description="Device not found",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            )
        }
    )
    def create(self, request):
        """Create settings for a device"""
        # Explicitly check if user is admin or manager
        if not request.user.role or request.user.role.name not in ['admin', 'manager']:
            return Response({
                'success': False,
                'message': 'Only admin or manager users are allowed to modify data.'
            }, status=status.HTTP_403_FORBIDDEN)

        serializer = UpdateDeviceSettingsSerializer(data=request.data)

        if serializer.is_valid():
            # Check if device exists
            device_id = request.data.get('deviceId')
            if not device_id:
                return Response({
                    'success': False,
                    'message': 'Device ID is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            try:
                device = DeviceMaster.objects.get(display_device_id=device_id)

                # Update device name if provided
                if 'name' in serializer.validated_data:
                    device.device_name = serializer.validated_data['name']

                # Update signal period if provided
                if 'signalPeriod' in serializer.validated_data:
                    device.signal_period = serializer.validated_data['signalPeriod']

                # Update approach area distance if provided
                if 'approachAreaDistance' in serializer.validated_data:
                    device.approach_area_distance = serializer.validated_data['approachAreaDistance']

                # Update approach area seconds if provided
                if 'approachAreaSeconds' in serializer.validated_data:
                    device.approach_area_seconds = serializer.validated_data['approachAreaSeconds']

                device.save()

                # Get user ID from authenticated user
                try:
                    # Import required modules
                    from django.db import IntegrityError
                    from django.utils import timezone

                    # Get the authenticated user's username
                    username = request.user.username
                    # Find the corresponding UserMaster record
                    user = UserMaster.objects.get(id=username)
                    # Set the user_id field on the device
                    device.user_id = user
                    device.save()  # Save the device with the updated user_id

                    # Now update settings with the user ID
                    user_id = user.id

                    # Update approach distance
                    distance_mm = int(serializer.validated_data['approachDistance'] * 1000)  # Convert meters to mm
                    try:
                        # Try to find existing setting with either user_id or user_id_neighborhoodThreshold
                        distance_setting = SettingsInfo.objects.filter(
                            key_name='neighborhoodThreshold'
                        ).filter(
                            id__in=[user_id, f"{user_id}_neighborhoodThreshold"]
                        ).first()

                        if distance_setting:
                            # Update existing setting
                            distance_setting.value = str(distance_mm)
                            distance_setting.updated_at = timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                            distance_setting.save()
                        else:
                            # Try to create with user_id as id
                            try:
                                distance_setting = SettingsInfo.objects.create(
                                    id=user_id,
                                    key_name='neighborhoodThreshold',
                                    value=str(distance_mm),
                                    created_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                                    updated_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                                )
                            except IntegrityError:
                                # If that fails, create with a unique ID
                                distance_setting = SettingsInfo.objects.create(
                                    id=f"{user_id}_neighborhoodThreshold",
                                    key_name='neighborhoodThreshold',
                                    value=str(distance_mm),
                                    created_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                                    updated_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                                )
                    except Exception as e:
                        print(f"Error updating neighborhoodThreshold: {e}")

                    # Update approach seconds
                    seconds = serializer.validated_data['approachSeconds']
                    try:
                        # Try to find existing setting with either user_id or user_id_estimationSec
                        seconds_setting = SettingsInfo.objects.filter(
                            key_name='estimationSec'
                        ).filter(
                            id__in=[user_id, f"{user_id}_estimationSec"]
                        ).first()

                        if seconds_setting:
                            # Update existing setting
                            seconds_setting.value = str(seconds)
                            seconds_setting.updated_at = timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                            seconds_setting.save()
                        else:
                            # Try to create with user_id as id
                            try:
                                seconds_setting = SettingsInfo.objects.create(
                                    id=user_id,
                                    key_name='estimationSec',
                                    value=str(seconds),
                                    created_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                                    updated_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                                )
                            except IntegrityError:
                                # If that fails, create with a unique ID
                                seconds_setting = SettingsInfo.objects.create(
                                    id=f"{user_id}_estimationSec",
                                    key_name='estimationSec',
                                    value=str(seconds),
                                    created_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                                    updated_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                                )
                    except Exception as e:
                        print(f"Error updating estimationSec: {e}")
                except UserMaster.DoesNotExist:
                    # If no UserMaster record exists for this user, log a warning but continue
                    print(f"Warning: No UserMaster record found for authenticated user '{request.user.username}'")
                    # We don't update settings in this case

                # Return created settings
                response_serializer = DeviceSettingsSerializer(device)
                return Response({
                    'success': True,
                    'message': 'デバイス設定が作成されました',
                    'data': response_serializer.data
                }, status=status.HTTP_201_CREATED)

            except DeviceMaster.DoesNotExist:
                return Response({
                    'success': False,
                    'message': 'Device not found'
                }, status=status.HTTP_404_NOT_FOUND)

        return Response({
            'success': False,
            'message': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        operation_summary="Update device settings",
        operation_description="Updates settings for a specific device. Requires admin or manager role.",
        request_body=UpdateDeviceSettingsSerializer,
        responses={
            200: openapi.Response(
                description="Settings updated successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Success message'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER, description='Device ID (integer)'),
                                'name': openapi.Schema(type=openapi.TYPE_STRING, description='Device name'),
                                'deviceId': openapi.Schema(type=openapi.TYPE_STRING, description='Display device ID'),
                                'workId': openapi.Schema(type=openapi.TYPE_INTEGER, description='Work ID (integer)', nullable=True),
                                'workName': openapi.Schema(type=openapi.TYPE_STRING, description='Work name', nullable=True),
                                'workTime': openapi.Schema(type=openapi.TYPE_STRING, description='Work time', nullable=True),
                                'battery': openapi.Schema(type=openapi.TYPE_INTEGER, description='Battery level', nullable=True),
                                'signalPeriod': openapi.Schema(type=openapi.TYPE_INTEGER, description='Signal period in seconds', nullable=True),
                                'approachDistance': openapi.Schema(type=openapi.TYPE_NUMBER, description='Approach distance in meters'),
                                'approachSeconds': openapi.Schema(type=openapi.TYPE_INTEGER, description='Approach time in seconds'),
                                'status': openapi.Schema(type=openapi.TYPE_STRING, description='Device status'),
                                'createdAt': openapi.Schema(type=openapi.TYPE_STRING, description='Creation timestamp', nullable=True),
                                'updatedAt': openapi.Schema(type=openapi.TYPE_STRING, description='Last update timestamp', nullable=True),
                            }
                        )
                    }
                )
            ),
            400: openapi.Response(
                description="Invalid input",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            ),
            404: openapi.Response(
                description="Device not found",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            )
        }
    )
    def update(self, request, pk=None):
        """Update settings for a specific device"""
        # Explicitly check if user is admin or manager
        print(f"User: {request.user.username}, Role: {request.user.role.name if request.user.role else 'None'}")
        if not request.user.role or request.user.role.name not in ['admin', 'manager']:
            print(f"Permission denied for user {request.user.username} with role {request.user.role.name if request.user.role else 'None'}")
            return Response({
                'success': False,
                'message': 'Only admin or manager users are allowed to modify data.'
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            device = DeviceMaster.objects.get(pk=pk)
            serializer = UpdateDeviceSettingsSerializer(data=request.data)

            if serializer.is_valid():
                # Update device fields if provided
                if 'name' in serializer.validated_data:
                    device.device_name = serializer.validated_data['name']

                if 'deviceId' in serializer.validated_data:
                    device.display_device_id = serializer.validated_data['deviceId']

                if 'signalPeriod' in serializer.validated_data:
                    device.signal_period = serializer.validated_data['signalPeriod']

                if 'approachAreaDistance' in serializer.validated_data:
                    device.approach_area_distance = serializer.validated_data['approachAreaDistance']

                if 'approachAreaSeconds' in serializer.validated_data:
                    device.approach_area_seconds = serializer.validated_data['approachAreaSeconds']

                if 'status' in serializer.validated_data:
                    device.status = serializer.validated_data['status']

                # Get user ID from authenticated user
                try:
                    # Get the authenticated user's username
                    username = request.user.username
                    # Find the corresponding UserMaster record
                    user = UserMaster.objects.get(id=username)
                    # Set the user_id field on the device
                    device.user_id = user
                except UserMaster.DoesNotExist:
                    # If no UserMaster record exists for this user, log a warning but continue
                    print(f"Warning: No UserMaster record found for authenticated user '{username}'")
                    # We don't return an error here as we want to allow the update to proceed

                if 'workId' in serializer.validated_data:
                    try:
                        work = WorkInfo.objects.get(id=serializer.validated_data['workId'])
                        device.work_id = work
                    except WorkInfo.DoesNotExist:
                        return Response({
                            'success': False,
                            'message': f"Work with ID '{serializer.validated_data['workId']}' not found"
                        }, status=status.HTTP_400_BAD_REQUEST)

                if 'workTime' in serializer.validated_data:
                    device.work_time = serializer.validated_data['workTime']

                if 'battery' in serializer.validated_data:
                    device.battery = serializer.validated_data['battery']

                if 'previousAlertInstruction' in serializer.validated_data:
                    device.previous_alert_instruction = serializer.validated_data['previousAlertInstruction']

                # Update the updated_at field
                from django.utils import timezone
                device.updated_at = timezone.now().strftime('%Y-%m-%d %H:%M:%S')

                device.save()

                # Update settings
                user_id = device.user_id.id if device.user_id else None
                if user_id:
                    from django.db import IntegrityError
                    from django.utils import timezone

                    # Update approach distance
                    distance_mm = int(serializer.validated_data['approachDistance'] * 1000)  # Convert meters to mm
                    try:
                        # Try to find existing setting with either user_id or user_id_neighborhoodThreshold
                        distance_setting = SettingsInfo.objects.filter(
                            key_name='neighborhoodThreshold'
                        ).filter(
                            id__in=[user_id, f"{user_id}_neighborhoodThreshold"]
                        ).first()

                        if distance_setting:
                            # Update existing setting
                            distance_setting.value = str(distance_mm)
                            distance_setting.updated_at = timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                            distance_setting.save()
                            print(f"Updated neighborhoodThreshold setting: {distance_setting.id} = {distance_setting.value}")
                        else:
                            # Try to create with user_id as id
                            try:
                                distance_setting = SettingsInfo.objects.create(
                                    id=user_id,
                                    key_name='neighborhoodThreshold',
                                    value=str(distance_mm),
                                    created_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                                    updated_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                                )
                                print(f"Created neighborhoodThreshold setting: {distance_setting.id} = {distance_setting.value}")
                            except IntegrityError:
                                # If that fails, create with a unique ID
                                distance_setting = SettingsInfo.objects.create(
                                    id=f"{user_id}_neighborhoodThreshold",
                                    key_name='neighborhoodThreshold',
                                    value=str(distance_mm),
                                    created_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                                    updated_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                                )
                                print(f"Created neighborhoodThreshold setting with unique ID: {distance_setting.id} = {distance_setting.value}")
                    except Exception as e:
                        print(f"Error updating neighborhoodThreshold: {e}")

                    # Update approach seconds
                    seconds = serializer.validated_data['approachSeconds']
                    try:
                        # Try to find existing setting with either user_id or user_id_estimationSec
                        seconds_setting = SettingsInfo.objects.filter(
                            key_name='estimationSec'
                        ).filter(
                            id__in=[user_id, f"{user_id}_estimationSec"]
                        ).first()

                        if seconds_setting:
                            # Update existing setting
                            seconds_setting.value = str(seconds)
                            seconds_setting.updated_at = timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                            seconds_setting.save()
                            print(f"Updated estimationSec setting: {seconds_setting.id} = {seconds_setting.value}")
                        else:
                            # Try to create with user_id as id
                            try:
                                seconds_setting = SettingsInfo.objects.create(
                                    id=user_id,
                                    key_name='estimationSec',
                                    value=str(seconds),
                                    created_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                                    updated_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                                )
                                print(f"Created estimationSec setting: {seconds_setting.id} = {seconds_setting.value}")
                            except IntegrityError:
                                # If that fails, create with a unique ID
                                seconds_setting = SettingsInfo.objects.create(
                                    id=f"{user_id}_estimationSec",
                                    key_name='estimationSec',
                                    value=str(seconds),
                                    created_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                                    updated_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                                )
                                print(f"Created estimationSec setting with unique ID: {seconds_setting.id} = {seconds_setting.value}")
                    except Exception as e:
                        print(f"Error updating estimationSec: {e}")

                # Refresh the device from the database to get the latest settings
                device.refresh_from_db()

                # Create a custom response with the updated settings
                response_data = {
                    'id': device.id,
                    'name': device.device_name,
                    'deviceId': device.display_device_id,
                    'userId': device.user_id.id if device.user_id else None,
                    'workId': device.work_id.id if device.work_id else None,
                    'workName': device.work_id.work_name if device.work_id else None,
                    'workTime': device.work_time,
                    'battery': device.battery,
                    'previousAlertInstruction': device.previous_alert_instruction,
                    'signalPeriod': device.signal_period,
                    'approachAreaDistance': device.approach_area_distance,
                    'approachAreaSeconds': device.approach_area_seconds,
                    'status': device.status or 'active',
                    'createdAt': str(device.created_at) if device.created_at else None,
                    'updatedAt': str(device.updated_at) if device.updated_at else None,
                }

                # Add the updated settings values
                if user_id:
                    # Get approach distance from settings
                    distance_setting = SettingsInfo.objects.filter(
                        key_name='neighborhoodThreshold'
                    ).filter(
                        id__in=[user_id, f"{user_id}_neighborhoodThreshold"]
                    ).first()

                    if distance_setting:
                        response_data['approachDistance'] = float(distance_setting.value) / 1000  # Convert mm to meters
                    else:
                        response_data['approachDistance'] = 5.0  # Default value

                    # Get approach seconds from settings
                    seconds_setting = SettingsInfo.objects.filter(
                        key_name='estimationSec'
                    ).filter(
                        id__in=[user_id, f"{user_id}_estimationSec"]
                    ).first()

                    if seconds_setting:
                        response_data['approachSeconds'] = int(seconds_setting.value)
                    else:
                        response_data['approachSeconds'] = device.signal_period or 30
                else:
                    response_data['approachDistance'] = 5.0  # Default value
                    response_data['approachSeconds'] = device.signal_period or 30

                return Response({
                    'success': True,
                    'message': 'デバイス設定が更新されました',
                    'data': response_data
                })

            return Response({
                'success': False,
                'message': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        except DeviceMaster.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Device not found'
            }, status=status.HTTP_404_NOT_FOUND)


class WorkPagination(PageNumberPagination):
    """Pagination class for works"""
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100


class WorksView(APIView):
    """
    View for works/operations

    This view provides endpoints to:
    - List all works (with optional pagination and detailed information)
    - Create a new work
    """
    permission_classes = [IsAdminOrManager]
    pagination_class = WorkPagination

    @swagger_auto_schema(
        operation_summary="Get all works",
        operation_description="Retrieves a list of all works. Supports two modes: simple list of work names or paginated detailed list. Requires admin or manager role.",
        manual_parameters=[
            openapi.Parameter(
                name='detailed',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                description='If true, returns detailed information about all works with pagination',
                required=False
            ),
            openapi.Parameter(
                name='page',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                description='Page number (only used when detailed=true)',
                required=False
            ),
            openapi.Parameter(
                name='page_size',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                description='Number of items per page (max 100, only used when detailed=true)',
                required=False
            )
        ],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'works': openapi.Schema(
                                    type=openapi.TYPE_ARRAY,
                                    items=openapi.Schema(type=openapi.TYPE_STRING),
                                    description='List of work names (when detailed=false)'
                                )
                            }
                        )
                    }
                )
            )
        }
    )
    def get(self, request):
        """Get all works/operations"""
        # Check if detailed view is requested
        detailed = request.query_params.get('detailed', 'false').lower() == 'true'

        if detailed:
            # Return paginated detailed list
            paginator = self.pagination_class()
            works = WorkInfo.objects.all().order_by('id')
            result_page = paginator.paginate_queryset(works, request)
            serializer = WorkInfoSerializer(result_page, many=True)  # Use WorkInfoSerializer to get all fields

            return paginator.get_paginated_response({
                'success': True,
                'data': serializer.data
            })
        else:
            # Return simple list of work names (original behavior)
            works = WorkInfo.objects.all()  # Fetch all works
            serializer = WorkInfoSerializer(works, many=True)  # Serialize all fields
            return Response({
                'success': True,
                'data': {
                    'works': serializer.data
                }
            })

    @swagger_auto_schema(
        operation_summary="Create a new work",
        operation_description="Creates a new work. Requires admin or manager role. The ID field is auto-generated by Django and should NOT be included in the request.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['work_name', 'group_num'],
            properties={
                'work_name': openapi.Schema(type=openapi.TYPE_STRING, description='Name of the work (required)'),
                'group_num': openapi.Schema(type=openapi.TYPE_STRING, description='Group number (required)')
            }
        ),
        responses={
            201: openapi.Response(
                description="Work created successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Success message'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER, description='Auto-generated Work ID (integer)'),
                                'work_name': openapi.Schema(type=openapi.TYPE_STRING, description='Work name'),
                                'group_num': openapi.Schema(type=openapi.TYPE_STRING, description='Group number')
                            }
                        )
                    }
                )
            ),
            400: openapi.Response(
                description="Invalid input",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            )
        }
    )
    def post(self, request):
        """Create a new work"""
        serializer = WorkInfoSerializer(data=request.data)

        if serializer.is_valid():
            work = serializer.save()
            return Response({
                'success': True,
                'message': 'Work created successfully',
                'data': serializer.data
            }, status=status.HTTP_201_CREATED)

        return Response({
            'success': False,
            'message': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)



class WorkDetailView(APIView):
    """
    View for work details

    This view provides endpoints to:
    - Get detailed information for a specific work
    - Delete a specific work
    """
    permission_classes = [IsAdminOrManager]

    @swagger_auto_schema(
        operation_summary="Get work details by ID",
        operation_description="Retrieves detailed information for a specific work by ID. Requires admin or manager role.",
        manual_parameters=[
            openapi.Parameter(
                name='work_id',
                in_=openapi.IN_PATH,
                type=openapi.TYPE_INTEGER,
                description='Work ID (integer)',
                required=True
            )
        ],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER, description='Work ID (integer)'),
                                'workName': openapi.Schema(type=openapi.TYPE_STRING, description='Work name'),
                                'groupNumber': openapi.Schema(type=openapi.TYPE_STRING, description='Group number'),
                                'assignedDevices': openapi.Schema(type=openapi.TYPE_INTEGER, description='Number of assigned devices'),
                                'timeElapsed': openapi.Schema(type=openapi.TYPE_STRING, description='Time elapsed'),
                                'accessibleAreas': openapi.Schema(type=openapi.TYPE_INTEGER, description='Number of accessible areas')
                            }
                        )
                    }
                )
            ),
            404: openapi.Response(
                description="Work not found",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            )
        }
    )
    def get(self, request, work_id):
        """Get detailed information for a specific work"""
        try:
            work = WorkInfo.objects.get(id=work_id)
            serializer = WorkGroupSerializer(work)
            return Response({
                'success': True,
                'data': serializer.data
            })
        except WorkInfo.DoesNotExist:
            return Response({
                'success': False,
                'message': f"Work with ID '{work_id}' not found"
            }, status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        operation_summary="Delete a work by ID",
        operation_description="Deletes a specific work by ID. Requires admin or manager role.",
        manual_parameters=[
            openapi.Parameter(
                name='work_id',
                in_=openapi.IN_PATH,
                type=openapi.TYPE_INTEGER,
                description='Work ID (integer)',
                required=True
            )
        ],
        responses={
            200: openapi.Response(
                description="Work deleted successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Success message')
                    }
                )
            ),
            404: openapi.Response(
                description="Work not found",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            ),
            400: openapi.Response(
                description="Cannot delete work with assigned devices",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            )
        }
    )
    def delete(self, request, work_id):
        """Delete a specific work"""
        try:
            work = WorkInfo.objects.get(id=work_id)

            # Check if any devices are assigned to this work
            assigned_devices = DeviceMaster.objects.filter(work_id=work.id).count()
            if assigned_devices > 0:
                return Response({
                    'success': False,
                    'message': f"Cannot delete work with ID '{work_id}' because it has {assigned_devices} assigned devices"
                }, status=status.HTTP_400_BAD_REQUEST)

            # Store work name for the response message
            work_name = work.work_name

            # Delete the work
            work.delete()

            return Response({
                'success': True,
                'message': f"作業 '{work_name}' が削除されました"  # Work has been deleted
            })

        except WorkInfo.DoesNotExist:
            return Response({
                'success': False,
                'message': f"Work with ID '{work_id}' not found"
            }, status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        operation_summary="Update work by ID",
        operation_description="Updates a specific work by ID. Requires admin or manager role.",
        manual_parameters=[
            openapi.Parameter(
                name='work_id',
                in_=openapi.IN_PATH,
                type=openapi.TYPE_INTEGER,
                description='Work ID (integer)',
                required=True
            )
        ],
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'work_name': openapi.Schema(type=openapi.TYPE_STRING, description='Work name'),
                'group_num': openapi.Schema(type=openapi.TYPE_STRING, description='Group number')
            }
        ),
        responses={
            200: openapi.Response(
                description="Work updated successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Success message'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER, description='Work ID (integer)'),
                                'workName': openapi.Schema(type=openapi.TYPE_STRING, description='Work name'),
                                'groupNumber': openapi.Schema(type=openapi.TYPE_STRING, description='Group number'),
                                'assignedDevices': openapi.Schema(type=openapi.TYPE_INTEGER, description='Number of assigned devices'),
                                'timeElapsed': openapi.Schema(type=openapi.TYPE_STRING, description='Time elapsed'),
                                'accessibleAreas': openapi.Schema(type=openapi.TYPE_INTEGER, description='Number of accessible areas')
                            }
                        )
                    }
                )
            ),
            404: openapi.Response(
                description="Work not found",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            ),
            400: openapi.Response(
                description="Invalid input",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            )
        }
    )
    def put(self, request, work_id):
        """Update a specific work"""
        try:
            work = WorkInfo.objects.get(id=work_id)

            # Update work fields if provided
            if 'work_name' in request.data:
                work.work_name = request.data['work_name']

            if 'group_num' in request.data:
                work.group_num = request.data['group_num']

            work.save()

            # Return updated work data
            serializer = WorkGroupSerializer(work)
            return Response({
                'success': True,
                'message': 'Work updated successfully',
                'data': serializer.data
            })

        except WorkInfo.DoesNotExist:
            return Response({
                'success': False,
                'message': f"Work with ID '{work_id}' not found"
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'success': False,
                'message': f"Error updating work: {str(e)}"
            }, status=status.HTTP_400_BAD_REQUEST)


class DeviceAssignWorkView(APIView):
    """
    View for assigning work to a device

    This view provides an endpoint to:
    - Assign work to a device and increment assignedDevices count for the work
    """
    permission_classes = [IsAdminOrManager]

    @swagger_auto_schema(
        operation_summary="Assign work to a device",
        operation_description="Assigns work to a device and increments assignedDevices count for the work. Requires admin or manager role.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['deviceId', 'assignedWork'],
            properties={
                'deviceId': openapi.Schema(type=openapi.TYPE_STRING, description='Device ID (display_device_id)'),
                'assignedWork': openapi.Schema(type=openapi.TYPE_STRING, description='Work name')
            }
        ),
        responses={
            200: openapi.Response(
                description="Work assigned successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Success message'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER, description='Device ID'),
                                'name': openapi.Schema(type=openapi.TYPE_STRING, description='Device name'),
                                'deviceId': openapi.Schema(type=openapi.TYPE_STRING, description='Device ID (display_device_id)'),
                                'assignedWork': openapi.Schema(type=openapi.TYPE_STRING, description='Assigned work name'),
                                'charge': openapi.Schema(type=openapi.TYPE_INTEGER, description='Battery charge'),
                                'status': openapi.Schema(type=openapi.TYPE_STRING, description='Device status'),
                                'usageTime': openapi.Schema(type=openapi.TYPE_STRING, description='Usage time')
                            }
                        )
                    }
                )
            ),
            404: openapi.Response(
                description="Device or work not found",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            ),
            400: openapi.Response(
                description="Invalid input",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            )
        }
    )
    def post(self, request):
        """Assign work to a device"""
        device_id = request.data.get('deviceId')
        if not device_id:
            return Response({
                'success': False,
                'message': 'Device ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        #assign deviceId to display_device_id
        request.data['display_device_id'] = device_id
        del request.data['deviceId']

        work_id = request.data.get('assignedWork')
        if not work_id:
            return Response({
                'success': False,
                'message': 'Work ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        #assign workId to work_id
        request.data['work_id'] = work_id
        del request.data['assignedWork']

        serializer = DeviceMasterSerializer(data=request.data)

        if serializer.is_valid():
            device_id = serializer.validated_data['display_device_id']
            work = serializer.validated_data['work_id']


            # Find the work
            work = WorkInfo.objects.filter(id=work.id).first()

            if not work:
                return Response({
                    'success': False,
                    'message': f"Work '{work_id}' not found"
                }, status=status.HTTP_404_NOT_FOUND)


            if DeviceMaster.objects.filter(work_id=work.id, display_device_id=device_id).exists():
                return Response({
                    'success': False,
                    'message': f"Device with ID '{device_id}' is already assigned to work '{work.work_name}'"
                }, status=status.HTTP_400_BAD_REQUEST)

            device, created = DeviceMaster.objects.get_or_create(
                display_device_id=device_id,
                defaults={
                    'device_name': request.data.get('name', 'Unnamed Device'),
                    'signal_period': request.data.get('signalPeriod', 60),
                    'work_time': request.data.get('workTime', None),
                    'battery': request.data.get('battery', None),
                    'previous_alert_instruction': request.data.get('previousAlertInstruction', None)
                }
            )

            # Update fields if the device already exists and data is provided
            if not created:
                if 'name' in request.data:
                    device.device_name = request.data['name']
                if 'signalPeriod' in request.data:
                    device.signal_period = request.data['signalPeriod']
                if 'workTime' in request.data:
                    device.work_time = request.data['workTime']
                if 'battery' in request.data:
                    device.battery = request.data['battery']
                if 'previousAlertInstruction' in request.data:
                    device.previous_alert_instruction = request.data['previousAlertInstruction']
                device.save()

            # Assign work to the device
            device.work_id = work
            device.save()

            # Serialize the updated device
            device_serializer = DeviceMasterSerializer(device)
            return Response({
                'success': True,
                'message': 'Device work assigned successfully',
                'data': device_serializer.data
            })

        return Response({
            'success': False,
            'message': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

@method_decorator(csrf_exempt, name='dispatch')
class DeviceRemoveWorkView(APIView):
    """
    View for removing work from a device

    This view provides an endpoint to:
    - Remove work from a device and decrement assignedDevices count for the work
    """
    permission_classes = [IsAdminOrManager]

    @swagger_auto_schema(
        operation_summary="Remove work from a device",
        operation_description="Removes work from a device and decrements assignedDevices count for the work. Requires admin or manager role.",
        request_body=DeviceMasterSerializer,
        responses={
            200: openapi.Response(
                description="Work removed successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Success message'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER, description='Device ID'),
                                'name': openapi.Schema(type=openapi.TYPE_STRING, description='Device name'),
                                'deviceId': openapi.Schema(type=openapi.TYPE_STRING, description='Device ID (display_device_id)'),
                                'assignedWork': openapi.Schema(type=openapi.TYPE_STRING, description='Assigned work name', nullable=True),
                                'charge': openapi.Schema(type=openapi.TYPE_INTEGER, description='Battery charge'),
                                'status': openapi.Schema(type=openapi.TYPE_STRING, description='Device status'),
                                'usageTime': openapi.Schema(type=openapi.TYPE_STRING, description='Usage time')
                            }
                        )
                    }
                )
            ),
            404: openapi.Response(
                description="Device not found",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            ),
            400: openapi.Response(
                description="Invalid input",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            )
        }
    )
    def post(self, request):
        """Remove work from a device"""
        #from the request display_device_id is coming as deviceId
        device_id = request.data.get('deviceId')
        if not device_id:
            return Response({
                'success': False,
                'message': 'Device ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        #assign deviceId to display_device_id
        request.data['display_device_id'] = device_id
        del request.data['deviceId']

        #work_id is coming as workId
        work_id = request.data.get('workId')
        if not work_id:
            return Response({
                'success': False,
                'message': 'Work ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        #assign workId to work_id
        request.data['work_id'] = work_id
        del request.data['workId']

        serializer = DeviceMasterSerializer(data=request.data)

        if serializer.is_valid():
            # device_id = serializer.validated_data['deviceId']

            # Find the device
            device = DeviceMaster.objects.filter(display_device_id=device_id).first()
            if not device:
                return Response({
                    'success': False,
                    'message': f"Device with ID '{device_id}' not found"
                }, status=status.HTTP_404_NOT_FOUND)

            # Remove work assignment
            device.work_id = None
            device.save()

            # Return updated device
            device_serializer = DeviceMasterSerializer(device)
            return Response({
                'success': True,
                'message': 'Device work removed successfully',
                'data': device_serializer.data
            })

        return Response({
            'success': False,
            'message': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class DeviceStatusView(APIView):
    """
    View for getting device status

    This view provides an endpoint to:
    - Get the current status of a device by its device ID
    """
    permission_classes = [IsAdminOrManager]

    @swagger_auto_schema(
        operation_summary="Get device status",
        operation_description="Gets the current status of a device by its device ID. Requires admin or manager role.",
        request_body=DeviceStatusSerializer,
        responses={
            200: openapi.Response(
                description="Device status retrieved successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Success message'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER, description='Device ID'),
                                'deviceId': openapi.Schema(type=openapi.TYPE_STRING, description='Device ID (display_device_id)'),
                                'status': openapi.Schema(type=openapi.TYPE_STRING, description='Device status')
                            }
                        )
                    }
                )
            ),
            404: openapi.Response(
                description="Device not found",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            ),
            400: openapi.Response(
                description="Invalid input",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            )
        }
    )
    def post(self, request):
        """Get device status"""
        serializer = DeviceStatusSerializer(data=request.data)

        if serializer.is_valid():
            device_id = serializer.validated_data['deviceId']
            work_id= serializer.validated_data.get('workId')

            # Find the device
            device = DeviceMaster.objects.filter(display_device_id=device_id, work_id__id=work_id).first()
            if not device:
                return Response({
                    'success': False,
                    'message': f"Device with ID '{device_id}' and work ID '{work_id}' not found"
                }, status=status.HTTP_404_NOT_FOUND)

            # Return all device data
            device_serializer = DeviceMasterSerializer(device)
            return Response({
                'success': True,
                'message': 'Device data retrieved successfully',
                'data': device_serializer.data
            })

        return Response({
            'success': False,
            'message': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


from rest_framework import viewsets, status
from rest_framework.response import Response
from .models import ActiveWork
from .serializers import ActiveWorkSerializer
# from rest_framework.permissions import IsAuthenticated # Uncomment if authentication is needed

class ActiveWorkViewSet(viewsets.ModelViewSet):
    """ViewSet for managing ActiveWork records"""
    queryset = ActiveWork.objects.all()
    serializer_class = ActiveWorkSerializer
    # permission_classes = [IsAuthenticated] # Add authentication if needed

    def create(self, request, *args, **kwargs):
        # Support creating a single object or a list of objects
        is_many = isinstance(request.data, list)
        serializer = self.get_serializer(data=request.data, many=is_many)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response({"success": True, "data": serializer.data}, status=status.HTTP_201_CREATED, headers=headers)

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response({"success": True, "data": serializer.data})

        serializer = self.get_serializer(queryset, many=True)
        return Response({"success": True, "data": serializer.data})

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response({"success": True, "data": serializer.data})

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, '_prefetched_objects_cache', None):
            instance._prefetched_objects_cache = {}

        return Response({"success": True, "data": serializer.data})

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        # Standard practice is to return 204 No Content on successful deletion
        return Response({"success": True, "message": "ActiveWork deleted successfully."}, status=status.HTTP_204_NO_CONTENT)


class WorkTimeCalculationView(APIView):
    """
    View for work time calculation

    This view provides endpoints to:
    - Get work information with calculated time
    """
    permission_classes = [IsAdminOrManager]

    def get(self, request, work_id):
        """Get work information with calculated time"""
        try:
            work = WorkInfo.objects.get(id=work_id)
            
            # Calculate time information
            current_time = timezone.now()
            created_at = work.created_at if work.created_at else current_time
            
            # Parse created_at if it's a string
            if isinstance(created_at, str):
                try:
                    created_at = datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
                    created_at = timezone.make_aware(created_at)
                except ValueError:
                    created_at = current_time
            
            # Calculate elapsed time since creation
            elapsed_since_creation = current_time - created_at
            elapsed_time_str = str(elapsed_since_creation).split('.')[0]  # Remove microseconds
            
            # For demo purposes, assume no reset time
            last_reset_time = None
            elapsed_since_reset = None
            
            # Count assigned devices and accessible areas
            assigned_devices = DeviceMaster.objects.filter(work_id=work.id).count()
            accessible_areas = PermitedApproachInfo.objects.filter(work_id=work.id).count()
            
            response_data = {
                'id': work.id,
                'work_name': work.work_name,
                'group_number': work.group_num,
                'created_at': created_at.isoformat(),
                'last_reset_time': last_reset_time,
                'current_time': current_time.isoformat(),
                'elapsed_time_since_creation': elapsed_time_str,
                'elapsed_time_since_reset': elapsed_since_reset,
                'total_calculated_time': elapsed_time_str,
                'assigned_devices': assigned_devices,
                'accessible_areas': accessible_areas,
                'status': 'active'
            }
            
            return Response({
                'success': True,
                'data': response_data
            })
            
        except WorkInfo.DoesNotExist:
            return Response({
                'success': False,
                'message': f'Work with ID {work_id} not found'
            }, status=status.HTTP_404_NOT_FOUND)


class ActiveWorksTimeCalculationView(APIView):
    """
    View for active works time calculation

    This view provides endpoints to:
    - Get all active works with calculated time
    """
    permission_classes = [IsAdminOrManager]

    def get(self, request):
        """Get all active works with calculated time"""
        works = WorkInfo.objects.all()  # Assuming all works are active for demo
        current_time = timezone.now()
        
        response_data = []
        
        for work in works:
            created_at = work.created_at if work.created_at else current_time
            
            # Parse created_at if it's a string
            if isinstance(created_at, str):
                try:
                    created_at = datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
                    created_at = timezone.make_aware(created_at)
                except ValueError:
                    created_at = current_time
            
            # Calculate elapsed time since creation
            elapsed_since_creation = current_time - created_at
            elapsed_time_str = str(elapsed_since_creation).split('.')[0]  # Remove microseconds
            
            # For demo purposes, assume no reset time
            last_reset_time = None
            elapsed_since_reset = None
            
            # Count assigned devices and accessible areas
            assigned_devices = DeviceMaster.objects.filter(work_id=work.id).count()
            accessible_areas = PermitedApproachInfo.objects.filter(work_id=work.id).count()
            
            work_data = {
                'id': work.id,
                'work_name': work.work_name,
                'group_number': work.group_num,
                'created_at': created_at.isoformat(),
                'last_reset_time': last_reset_time,
                'current_time': current_time.isoformat(),
                'elapsed_time_since_creation': elapsed_time_str,
                'elapsed_time_since_reset': elapsed_since_reset,
                'total_calculated_time': elapsed_time_str,
                'assigned_devices': assigned_devices,
                'accessible_areas': accessible_areas,
                'status': 'active'
            }
            
            response_data.append(work_data)
        
        return Response({
            'success': True,
            'data': response_data
        })


class WorkTimerUpdateView(APIView):
    """
    View for updating work timer

    This view provides endpoints to:
    - Update work timer with actions: start, pause, resume, reset
    """
    permission_classes = [IsAdminOrManager]

    def put(self, request, work_id):
        """Update work timer"""
        try:
            work = WorkInfo.objects.get(id=work_id)
            
            action = request.data.get('action')
            timestamp = request.data.get('timestamp')
            
            if not action:
                return Response({
                    'success': False,
                    'message': 'Action is required'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Parse timestamp if provided
            if timestamp:
                try:
                    timestamp_dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                except ValueError:
                    return Response({
                        'success': False,
                        'message': 'Invalid timestamp format'
                    }, status=status.HTTP_400_BAD_REQUEST)
            else:
                timestamp_dt = timezone.now()
            
            # Handle different actions
            current_status = 'running'
            elapsed_time = "00:00:00"
            
            if action == 'start':
                current_status = 'running'
                work.updated_at = timestamp_dt.strftime('%Y-%m-%d %H:%M:%S')
            elif action == 'pause':
                current_status = 'paused'
            elif action == 'resume':
                current_status = 'running'
            elif action == 'reset':
                current_status = 'stopped'
                elapsed_time = "00:00:00"
                work.updated_at = timestamp_dt.strftime('%Y-%m-%d %H:%M:%S')
            else:
                return Response({
                    'success': False,
                    'message': 'Invalid action. Allowed actions: start, pause, resume, reset'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            work.save()
            
            return Response({
                'success': True,
                'message': 'Work timer updated successfully',
                'data': {
                    'work_id': work.id,
                    'action': action,
                    'timestamp': timestamp_dt.isoformat(),
                    'current_status': current_status,
                    'elapsed_time': elapsed_time
                }
            })
            
        except WorkInfo.DoesNotExist:
            return Response({
                'success': False,
                'message': f'Work with ID {work_id} not found'
            }, status=status.HTTP_404_NOT_FOUND)


class AlertViewSet(viewsets.ModelViewSet):
    """ViewSet for Alert (HistoricalJudgeAlertInstruction) model with full CRUD operations"""
    queryset = HistoricalJudgeAlertInstruction.objects.all()
    serializer_class = AlertSerializer
    permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'put', 'delete']

    def get_serializer_class(self):
        if self.action == 'create':
            return AlertCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return AlertUpdateSerializer
        return AlertSerializer

    def list(self, request):
        """Get all alerts"""
        alerts = self.get_queryset().order_by('-created_at')
        serializer = self.get_serializer(alerts, many=True)
        return Response({
            'success': True,
            'data': {
                'alerts': serializer.data
            }
        })

    def create(self, request):
        """Create a new alert"""
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            # Get device
            device_id = serializer.validated_data['device']
            try:
                device = DeviceMaster.objects.get(id=device_id)
            except DeviceMaster.DoesNotExist:
                return Response({
                    'success': False,
                    'message': f'Device with ID {device_id} not found'
                }, status=status.HTTP_404_NOT_FOUND)

            # Map alert_type to alert_instruction
            alert_type = serializer.validated_data['alert_type']
            alert_instruction_map = {
                'approach': 1,
                'entry': 2,
                'battery_low': 3,
                'general': 0
            }
            alert_instruction = alert_instruction_map.get(alert_type, 0)

            # Create alert with current timestamp
            from django.utils import timezone
            import uuid

            alert = HistoricalJudgeAlertInstruction.objects.create(
                id=str(uuid.uuid4())[:20],  # Generate unique ID
                device_id=device,
                alert_instruction=alert_instruction,
                created_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                updated_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S')
            )

            # Return success response
            response_serializer = AlertSerializer(alert)
            return Response({
                'success': True,
                'data': response_serializer.data
            }, status=status.HTTP_201_CREATED)

        return Response({
            'success': False,
            'message': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def retrieve(self, request, pk=None):
        """Get alert by ID"""
        try:
            alert = self.get_object()
            serializer = self.get_serializer(alert)
            return Response({
                'success': True,
                'data': serializer.data
            })
        except HistoricalJudgeAlertInstruction.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Alert not found'
            }, status=status.HTTP_404_NOT_FOUND)

    def update(self, request, pk=None):
        """Update alert (PUT)"""
        try:
            alert = self.get_object()
            serializer = self.get_serializer(data=request.data)

            if serializer.is_valid():
                # Update device if provided
                if 'device' in serializer.validated_data:
                    device_id = serializer.validated_data['device']
                    try:
                        device = DeviceMaster.objects.get(id=device_id)
                        alert.device_id = device
                    except DeviceMaster.DoesNotExist:
                        return Response({
                            'success': False,
                            'message': f'Device with ID {device_id} not found'
                        }, status=status.HTTP_404_NOT_FOUND)

                # Update alert_instruction if alert_type provided
                if 'alert_type' in serializer.validated_data:
                    alert_type = serializer.validated_data['alert_type']
                    alert_instruction_map = {
                        'approach': 1,
                        'entry': 2,
                        'battery_low': 3,
                        'general': 0
                    }
                    alert.alert_instruction = alert_instruction_map.get(alert_type, 0)

                # Update timestamp
                from django.utils import timezone
                alert.updated_at = timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                alert.save()

                response_serializer = AlertSerializer(alert)
                return Response({
                    'success': True,
                    'data': response_serializer.data
                })

            return Response({
                'success': False,
                'message': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        except HistoricalJudgeAlertInstruction.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Alert not found'
            }, status=status.HTTP_404_NOT_FOUND)

    def destroy(self, request, pk=None):
        """Delete alert"""
        try:
            alert = self.get_object()
            alert.delete()
            return Response({
                'success': True,
                'message': 'Alert deleted successfully'
            })
        except HistoricalJudgeAlertInstruction.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Alert not found'
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['post'], url_path='mark-read')
    def mark_as_read(self, request, pk=None):
        """Mark alert as read"""
        try:
            alert = self.get_object()
            # Since there's no is_read field in the model, we just acknowledge the request
            # In a real implementation, you might add an is_read field to the model
            return Response({
                'success': True,
                'message': 'Alert marked as read successfully'
            })
        except HistoricalJudgeAlertInstruction.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Alert not found'
            }, status=status.HTTP_404_NOT_FOUND)
